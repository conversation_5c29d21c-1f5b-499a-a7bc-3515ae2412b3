package com.dcai.aixg.impl;

import com.alibaba.fastjson.JSON;
import com.dcai.aixg.domain.task.Task;
import com.dcai.aixg.domain.task.TaskRpt;
import com.dcai.aixg.dto.BrokerDTO;
import com.dcai.aixg.dto.LoginInfoDTO;
import com.dcai.aixg.dto.task.TaskDTO;
import com.dcai.aixg.integration.wechat.WechatService;
import com.dcai.aixg.pro.EditBrokerPO;
import com.dcai.aixg.pro.EditProjectPO;
import com.dcai.aixg.pro.LoginInfoPO;
import com.dcai.aixg.pro.UpdateCityPo;
import com.dcai.aixg.pro.task.CreateTaskCallBackPO;
import com.dcai.aixg.pro.task.WriteSharePO;
import com.dcai.gateway.sdk.GatewaySDK;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.community.BindCommunityPO;
import com.ejuetc.consumer.api.community.CommunityAPI;
import com.ejuetc.consumer.api.delegation.ApiQueryListPO;
import com.ejuetc.consumer.api.delegation.DelegationAPI;
import com.ejuetc.consumer.api.dto.DelegationDictDTO;
import com.ejuetc.consumer.web.vo.CommunityVO;
import com.ejuetc.consumer.web.vo.DelegationVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static java.time.LocalDateTime.now;


@RunWith(SpringRunner.class)
@SpringBootTest
public class BrokerTest {

    @Autowired
    private WriteImpl writeImpl;

    @Autowired
    private BrokerImpl brokerImpl;
    @Autowired
    private ExternalImpl externalImpl;
//    private BrokerAPI brokerAPI = getAPI(BrokerAPI.class, "http://10.123.159.50:8097");

    @Autowired
    @Qualifier("delegationAPI_SaaSAPIFeign")
    private DelegationAPI delegationAPI;

    @Autowired
    @Qualifier("communityAPI_SaaSAPIFeign")
    private CommunityAPI communityAPI;

    @Autowired
    private GatewaySDK gatewaySDK;
    @Value("${dcai.aixg.saasapi.feign-client.consumer-server-url}")
    private String consumerUrl;



    @Test
    public void getWxOpenId() {
        ApiResponse<?> response = brokerImpl.getWxOpenId("0a11OA0w3kHa153oXF0w3cGjAL21OA0m");
        System.out.println(response);
    }

    @Test
    public void sendCode(){
        ApiResponse<?> response = brokerImpl.sendCode("18916694185");
        System.out.println(response);
    }

    @Test
    public void testLogin() {
        ApiResponse<LoginInfoDTO> response = brokerImpl.login(new LoginInfoPO().setPhone("13524716276").setLoginType(2).setCode("123456"));
        System.out.println(response);
    }

    @Test
    public void testQuery() {
        ApiResponse<?> response = brokerImpl.queryInfo(new SaasLoginToken()
                .setToken("token FY:ACC:TOKEN:AIXG::{5115fc5b-9a19-4780-8007-aa966a76042e}")
                .setUserId(6612722971394985472L));
        System.out.println(response);
    }

    @Test
    public void testEdit(){
        ApiResponse<?> response = brokerImpl.edit(new SaasLoginToken()
                        .setToken("token FY:ACC:TOKEN:AIXG::{b7a92976-dcf3-4044-a6d7-a18fe957622b}")
                        .setUserId(9096743779674658563L),
                new EditBrokerPO().setName("123"));
        System.out.println(response);
    }

    @Test
    public void testUpdateCity() {
        ApiResponse<?> response = brokerImpl.updateCity(
                new SaasLoginToken()
                        .setToken("token FY:ACC:TOKEN:AIXG::{b7e5b986-94f1-4ba2-9aa3-2ae56479b2a9}")
                        .setUserId(9096691966195823619L),
                new UpdateCityPo().setCityId("310100").setCityName("上海市").setTownId("1451997").setTownName("大宁路街道"));
        System.out.println(response);
    }

    @Test
    public void testPointConfig() {
        ApiResponse<?> response = brokerImpl.pointConfig("");
        System.out.println(response);
    }

    @Test
    public void testPointDetail() {
        ApiResponse<?> response = brokerImpl.pointDetail(new SaasLoginToken()
                .setToken("token FY:ACC:TOKEN:AIXG::{b7e5b986-94f1-4ba2-9aa3-2ae56479b2a9}")
                .setUserId(9096691966195823619L),1,20);
        System.out.println(response);
    }

    @Test
    public void testWechatCallBack() {
        String response = brokerImpl.wechatCallback("b34bedd2e7dc29de457d520d186e22aef6f3d861","**********", "**********", "6873978722806627805", null);
        System.out.println(response);
    }

    @Test
    public void testEditProject() {
        for (int i = 0; i < 10; i++) {
            ApiResponse<BrokerDTO> response = brokerImpl.edit4api(new EditProjectPO()
//                .setId(1L)
                            .setPhone("李四_" + now())
            );
            System.out.println(JSON.toJSONString(response, true));
        }
    }

    @Test
    public void healthCheck() {
//        ApiResponse<?> response = projectImpl.healthCheck();
//        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void getQrCodeTicket() {
        String aa = getBean(WechatService.class).getQrCodeTicket(152L);
        System.out.println(aa);
    }

    @Test
    public void getTask() {
        Task response = brokerImpl.getTask("152");
        System.out.println(response);
    }

    @Test
    public void testTaskCallBack() {
        ApiResponse<TaskDTO>response = externalImpl.createTaskCallBack(new CreateTaskCallBackPO()
                .setTaskId("1949756774126014466")
                .setTaskType(TaskDTO.Type.WRITE)
                .setTaskStatus(TaskDTO.Status.DONE).setMsg("大宁蓄势待发：2025年7月静安大宁板块楼市全景分析"));
        System.out.println(response);
    }

    @Test
    public void testSendMessage() {
        ApiResponse<TaskDTO> response = externalImpl.testHandleSendMessage(3855L);
        System.out.println(response);
    }

    @Test
    public void testGeneratorImage() {
        List<String> result = getBean(TaskRpt.class).findById(3334L).get().generatorImage(List.of("1","2","3"));
        System.out.println(result);
    }

    @Test
    public void testCreateMonthlyMarketAnalysis() {
        brokerImpl.createMonthlyMarketAnalysis();
        System.out.println("111");
    }

    @Test
    public void testCreateAnalysisOfIndustryPolicies() {
        brokerImpl.createAnalysisOfIndustryPolicies("310100", "上海市", "公积金调整");
        System.out.println("111");
    }

    @Test
    public void testQueryDelegationDetail() {
        ApiResponse<List<String>> list = delegationAPI.list(DelegationDictDTO.Category.REDO);
        ApiResponse<List<DelegationVO>> response = delegationAPI.query(new ApiQueryListPO().setDelegationIds(List.of(53504L,53502L)));
        System.out.println(response);
    }

    @Test
    public void testShareRedNote() {
        ApiResponse<?> response = writeImpl.createWriteShare(new SaasLoginToken()
                .setToken("token FY:ACC:TOKEN:AIXG::{b7e5b986-94f1-4ba2-9aa3-2ae56479b2a9}")
                .setUserId(9096691966195823619L),
                new WriteSharePO().setTaskId(3589L).setSubType(TaskDTO.SubType.REDNOTE).setChapters(List.of("1","2","3","4","5","6"))
        );
        System.out.println(response);
    }

    @Test
    public void testBind() {
        ApiResponse<CommunityVO> response = communityAPI.bind(new BindCommunityPO().setAddress("上海市徐汇区沪闵路9191号(上海南站地铁站8号口步行428米)").setName("徐汇万科广场"));
        System.out.println(response);
    }
}