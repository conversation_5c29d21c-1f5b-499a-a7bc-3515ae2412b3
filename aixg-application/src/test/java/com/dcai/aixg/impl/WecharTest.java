package com.dcai.aixg.impl;


import com.dcai.aixg.integration.ewechat.AesException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class WecharTest {

    @Autowired
    private EWeCharImpl weChat;

    @Test
    public void verifyWecharURL() throws AesException {
        String signUrl = weChat.verifyCallback("5c45ff5e21c57e6ad56bac8758b79b1d9ac89fd3", "1409659589", "263014780", "P9nAzCzyDtyTWESHep1vC5X9xho/qYX3Zpb4yKa9SKld1DsH3Iyt3tP3zNdtp+4RPcs8TgAE7OaBO+FZXvnaqQ==");
        System.out.println("Sign URL: " + signUrl);
    }

}