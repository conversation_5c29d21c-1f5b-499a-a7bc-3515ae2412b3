spring:
  cloud:
    nacos:
      discovery:
        server-addr: mse-0c162cd0-nacos-ans.mse.aliyuncs.com:8848
        namespace: ${spring.application.business}
        group: ${spring.profiles.active}
      config:
        server-addr: mse-0c162cd0-nacos-ans.mse.aliyuncs.com:8848
        file-extension: yaml
        refresh-enabled: true
        namespace: ${spring.application.business}
        group: ${spring.application.name}
  config:
    import: nacos:${spring.application.name}-${spring.profiles.active}.yaml
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************
    username: dcai_aixg
    password: u9^r3E%4FFhj9LiyHvcs$7j
    hikari:
      maximum-pool-size: 10
      connection-timeout: 5000
  jpa:
    database-platform: org.hibernate.dialect.MySQLDialect
    hibernate:
      ddl-auto: update
    show-sql: true
    open-in-view: true
    properties:
      hibernate:
        format_sql: true
  data:
    redis:
      host: r-uf6ybvour5ydw8oldg.redis.rds.aliyuncs.com
      password: 097U8jHgt6%DGvjj*5rcNyirdExcn
      port: 6379
      timeout: 5000
      jedis:
        pool:
          max-active: 120
          max-wait: 3000
          min-idle: 5
          max-idle: 100
redis:
  saas:
    standalone: r-uf6ybvour5ydw8oldg.redis.rds.aliyuncs.com:6379
    password: 097U8jHgt6%DGvjj*5rcNyirdExcn
dcai:
  gateway:
    sdk:
      keyCode: "8c1eb01e696a49cba0dfdfce4f4507d8"
      keySecret: "vlTRIWGXyONtV9YB76aqer1FTCKV+g0Wa8J29vbzPng="
      serverUrl: "https://dcai.ebaas.com/gateway"
  message:
    url: "http://message-svc.dcai.svc.cluster.local/message"
  aixg:
    saasapi:
      feign-client:
        consumer-server-url: "http://ejuetc-consumer.ejucloud.cn/consumer"
    broker:
      propertyUrl: "https://pre-api.fangyou.com"
      merchantUrl: "https://pre-svc.fangyou.com"
    ker:
      url: "https://www.dichanai.com"
      client_id: "prod-ejuetc-km3druqsmj"
      client_secret: "sk-ZYfUxNOvFYBtwubaa5IixxpgyZndksIH"
    order:
      callBackUrl: "http://dcai.ebaas.com/aixg/api/order/orderPayCallBack"
    webUrl: "https://dcai.ebaas.com/cric/article/view?showBack=0&showBtnType=0&"
ejuetc:
  saasapi:
    sdk:
      key-code: "216da28ec52c47b78dace957e080160a"
      key-secret: "nDVvPMCtPK3uTRIlGdzUJddnQZicYWKKLcfnL/Q+rDU="
      host: "http://saasapi.ebaas.com"
  commons:
    oss:
      access-key-id: LTAI5tBnSSpJoSdU3kGfcgk6
      access-key-secret: ******************************
      oss-endpoint: oss-cn-shanghai.aliyuncs.com
      url-prefix: https://dcai-oss.ebaas.com/
      bucket-name: dcai-oss
      upload-functions:
        aixg-broker-photo:
          date-format: yyyyMMdd
          max-count: 5
          valid-seconds: 900
  loginFilter:
    enable: true
  login:
    processor:
      saas:
        enable: true
#  consumer:
#    url: "http://ejuetc-consumer.uat.ejucloud.cn/consumer"
#  saasApi:
#    keyCode: "216da28ec52c47b78dace957e080160a"
#    keySecret: "nDVvPMCtPK3uTRIlGdzUJddnQZicYWKKLcfnL/Q+rDU="
#    url: "http://saasapi-uat.ebaas.com"
