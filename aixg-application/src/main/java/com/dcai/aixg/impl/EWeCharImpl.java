package com.dcai.aixg.impl;

import com.dcai.aixg.api.EWeChar;
import com.dcai.aixg.integration.ewechat.WXBizMsgCrypt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.ThreadUtils.asyncExec;

/**
 * 工作流服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
@RestController
public class EWeCharImpl implements EWeChar {

    private final WXBizMsgCrypt wxcpt;

    @Override
    public String verifyCallback(String sign, String timestamp, String nonce, String echostr) {
        log.info("verifyCallback sign = {}, timestamp = {}, nonce = {}, echostr = {}", sign, timestamp, nonce, echostr);
        String verifyURL = wxcpt.VerifyURL(sign, timestamp, nonce, echostr);
        log.info("verifyCallback verifyURL = {}", verifyURL);
        return verifyURL;
    }

    @Override
    public String receiveCallback(String sign, String timestamp, String nonce, String echostr) {
        log.info("receiveCallback sign = {}, timestamp = {}, nonce = {}, echostr = {}", sign, timestamp, nonce, echostr);
        String decryptMsg = wxcpt.DecryptMsg(sign, timestamp, nonce, echostr);
        log.info("receiveCallback decryptMsg = {}", decryptMsg);
        return decryptMsg;
    }

}
