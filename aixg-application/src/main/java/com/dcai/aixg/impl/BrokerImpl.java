package com.dcai.aixg.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.BrokerAPI;
import com.dcai.aixg.domain.banner.Banner;
import com.dcai.aixg.domain.banner.BannerRpt;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.domain.broker.BrokerRpt;
import com.dcai.aixg.domain.task.Task;
import com.dcai.aixg.domain.task.TaskRpt;
import com.dcai.aixg.domain.tweet.Tweet;
import com.dcai.aixg.domain.tweet.TweetRpt;
import com.dcai.aixg.dto.*;
import com.dcai.aixg.dto.task.TaskDTO;
import com.dcai.aixg.integration.WxLoginApi;
import com.dcai.aixg.integration.wechat.WechatService;
import com.dcai.aixg.kerApi.KerApi;
import com.dcai.aixg.pro.*;
import com.dcai.gateway.sdk.GatewaySDK;
import com.dcai.message.api.api.MessageAPI;
import com.dcai.message.api.dto.MessageDTO;
import com.dcai.message.api.pro.SendMessagePO;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.commons.base.utils.StringUtils;
import com.ejuetc.commons.base.utils.ThreadUtils;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.*;
import java.security.MessageDigest;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.cacheSaasLoginInfo;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getAPI;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@RefreshScope
@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class BrokerImpl implements BrokerAPI {
    private final BrokerRpt brokerRpt;

    @Value("${dcai.aixg.broker.propertyUrl}")
    private String propertyUrl;

    @Value("${dcai.aixg.broker.merchantUrl}")
    private String merchantUrl;

    private final GatewaySDK gatewaySDK;

    @Value("${dcai.gateway.enabled:true}")
    private boolean gatewayEnabled;

    @Value("${dcai.message.url}")
    private String messageUrl;

    @Value("${dcai.aixg.broker.id}")
    private Long adminBrokerId;

    private final MessageAPI messageAPI;

    private final KerApi kerApi;

    public static final String codeKey = "aixg.broker.phone:";

    @Value("${wechat.token}")
    private String token;

    private final TaskRpt taskRpt;

    private final BannerRpt bannerRpt;

    private final TweetRpt tweetRpt;

    @Override
    public ApiResponse<WechatResp.WechatOpenId> getWxOpenId(String code) {
        ApiResponse<String> response = getAPI(WxLoginApi.class, propertyUrl).getAiXgOpenId(code);
        if (response.isFail()) {
            return ApiResponse.apiResponse(ResponseStatus.FAIL_BIZ, !StringUtils.isBlank(response.getMessage()) ? response.getMessage() : "获取用户openId失败", null);
        }
        String data = response.getData();
        JSONObject jsonObject = JSONObject.parseObject(data);
        return ApiResponse.succ(
                new WechatResp.WechatOpenId()
                .setOpenId(jsonObject.getString("openid"))
                .setUnionId(jsonObject.containsKey("unionid") ? jsonObject.getString("unionid") : null)
                .setSessionKey(jsonObject.getString("sessionKey"))
        );
    }

    @Override
    public ApiResponse<WechatResp.PhoneNumber> getWxPhone(@Valid WechatAuthorizationPhonePO po) {
        ApiResponse<String> response = getAPI(WxLoginApi.class, propertyUrl).getAiXgPhone(po);
        if (response.isFail()) {
            return ApiResponse.apiResponse(ResponseStatus.FAIL_BIZ, !StringUtils.isBlank(response.getMessage()) ? response.getMessage() : "获取用户openId失败", null);
        }
        String data = response.getData();
        JSONObject jsonObject = JSONObject.parseObject(data);
        return ApiResponse.succ(
               new WechatResp.PhoneNumber()
                        .setPhoneNumber(jsonObject.getString("phoneNumber"))
                        .setPurePhoneNumber(jsonObject.containsKey("purePhoneNumber") ? jsonObject.getString("purePhoneNumber") : null)
                        .setCountryCode(jsonObject.containsKey("countryCode") ? jsonObject.getString("countryCode") : null)
        );
    }


    @Override
    public ApiResponse<?> sendCode(String phone) {
        ThreadUtils.redisLock(20, 20, "aixg.broker.sendCode", phone);
        String code = generateRandomCode(6);
        String uuid = UUID.randomUUID().toString();
        log.info("sendCode phone = {}, code= {}", phone, code);
//        MessageAPI messageAPI = gatewaySDK.feignClient(MessageAPI.class, messageUrl);
        ApiResponse<List<MessageDTO>> response = messageAPI.sendMessage(
                new SendMessagePO(
                        "saas.authCode",
                        uuid,
                        Map.of(
                                "phone", phone,
                                "authCode", code,
                                "cityIds", "1")
                )
        );
        if (response.isFail()) {
            return ApiResponse.apiResponse(ResponseStatus.FAIL_BIZ, StringUtils.isBlank(response.getMessage()) ? "发送验证码失败" : response.getMessage(), null);
        }
        getBean(StringRedisTemplate.class).opsForValue().set(codeKey + phone, code);
        getBean(StringRedisTemplate.class).expire(codeKey + phone, 5, TimeUnit.MINUTES);
        return succ();
    }

    @Override
    public ApiResponse<LoginInfoDTO> login(@Valid LoginInfoPO po) {
        if (po.getLoginType() == 2) {
            Object redisCode = getBean(StringRedisTemplate.class).opsForValue().get(codeKey + po.getPhone());
            if (redisCode == null || !po.getCode().equals(redisCode.toString())) {
                throw new BusinessException("bc.cpm.aixg.1001");
            }
        }
        ApiResponse<?> response = getAPI(WxLoginApi.class, merchantUrl).login(po);
        if (response.isFail()) {
            return ApiResponse.apiResponse(ResponseStatus.FAIL_BIZ, !StringUtils.isBlank(response.getMessage()) ? response.getMessage() : "创建用户失败", null);
        }
        JSONObject jsonObject = JSONObject.parseObject(response.getData().toString());
        LoginInfoDTO loginInfo = new LoginInfoDTO()
                .setId(jsonObject.getLong("userId"))
                .setName(jsonObject.containsKey("name") ? jsonObject.getString("name") : null)
                .setToken(jsonObject.getString("token"))
                .setPhone(jsonObject.getString("phone"))
                .setIcon(jsonObject.containsKey("icon") ? jsonObject.getString("icon") : null)
                .setCompanyName(jsonObject.containsKey("companyName") ? jsonObject.getString("companyName") : null)
                .setCityId(jsonObject.containsKey("cityId") ? jsonObject.getString("cityId") : null)
                .setCityName(jsonObject.containsKey("cityName") ? jsonObject.getString("cityName") : null)
                .setOpenId(po.getOpenId());
        Broker broker = brokerRpt.findById(loginInfo.getId()).orElseGet(() -> {
            Long kerUserId = kerApi.getKerUserId(po.getPhone());
            return new Broker(loginInfo, kerUserId).save();
        });
        if (StringUtils.notBlank(po.getOpenId()) && StringUtils.isBlank(broker.getOpenId())) {
            broker.setOpenId(po.getOpenId());
        }
        String oldSaasLoginTokenStr = getBean(StringRedisTemplate.class).opsForValue().get(userInfoKey(loginInfo.getToken().substring(6), loginInfo.getId().toString()));
        if (StringUtils.notBlank(oldSaasLoginTokenStr)) {
            SaasLoginToken old = JSON.parseObject(oldSaasLoginTokenStr, SaasLoginToken.class);
            getBean(StringRedisTemplate.class).opsForValue().set(old.getToken().substring(6), "*");
        }
        SaasLoginToken saasLoginToken = new SaasLoginToken()
                .setToken(loginInfo.getToken())
                .setUserId(loginInfo.getId());
        cacheSaasLoginInfo(saasLoginToken);

        return succ(loginInfo);
    }

    @Override
    public ApiResponse<?> logout(SaasLoginToken saasLoginToken) {
        String subToken = saasLoginToken.getToken().substring(6);
        getBean(StringRedisTemplate.class).delete(subToken);
        getBean(StringRedisTemplate.class).delete(userInfoKey(subToken, saasLoginToken.getUserId().toString()));
        return succ();
    }

    private static String userInfoKey(String token, String userId) {
        return token.contains("SHARING:ACC:TOKEN") ? "SHARING:ACC:USER::{%s}".formatted(new Object[]{userId}) : "FY:ACC:USER::{%s}".formatted(new Object[]{userId});
    }

    @Override
    public ApiResponse<BrokerDTO> queryInfo(SaasLoginToken saasLoginToken) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        Long point = kerApi.getUserPoint(broker.getKerId());
        BrokerDTO brokerDTO = convert2DTO(broker, new BrokerDTO().setPoint(0L));
        brokerDTO.setPoint(point);
        ApiResponse<?> response = getAPI(WxLoginApi.class, merchantUrl).aiXgQuery(broker.getId());
        if (response != null && response.isSucc()) {
            JSONObject jsonObject = JSON.parseObject(response.getData().toString());
            brokerDTO.setMerchantType(jsonObject.getInteger("merchantType"));
            brokerDTO.setUserStatus(jsonObject.getInteger("userStatus"));
        }
        return succ(brokerDTO);
    }

    @Override
    public ApiResponse<BrokerDTO> queryInfo(Long userId) {
        SaasLoginToken saasLoginToken = new SaasLoginToken().setUserId(userId);
        return queryInfo(saasLoginToken);
    }

    @Override
    public ApiResponse<BrokerDTO> edit(SaasLoginToken saasLoginToken, @Valid EditBrokerPO editBrokerPO) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        ApiResponse<String> response = getAPI(WxLoginApi.class, merchantUrl).aiXgUpdateUser(saasLoginToken.getToken(), editBrokerPO);
        if (response == null || response.isFail()) {
            return ApiResponse.apiResponse(ResponseStatus.FAIL_BIZ, response != null && !StringUtils.isBlank(response.getMessage()) ? response.getMessage() : "更新信息失败", null);
        }
        broker.edit(editBrokerPO);
        Long point = kerApi.getUserPoint(broker.getKerId());
        BrokerDTO brokerDTO = convert2DTO(broker, new BrokerDTO().setPoint(0L));
        brokerDTO.setPoint(point);
        return succ(brokerDTO);
    }

    @Override
    public ApiResponse<BrokerDTO> updateCity(SaasLoginToken loginToken, UpdateCityPo po) {
        Broker broker = brokerRpt.findById(loginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
//        if (!StringUtils.isBlank(broker.getCityId()) && !StringUtils.isBlank(broker.getTownId())) {
//            log.info("updateCity city is not null loginToken ={}, id = {}, cityId = {}, param = {}", loginToken.getUserId(), loginToken, broker.getCityId(), po.getCityId());
//            return succ(convert2DTO(broker, new BrokerDTO().setPoint(0L)));
//        }
        if (StringUtils.isBlank(broker.getCityId())) {
            ApiResponse<String> response = getAPI(WxLoginApi.class, merchantUrl).updateCity(loginToken.getToken(), po);
            if (response == null || response.isFail()) {
                return ApiResponse.apiResponse(ResponseStatus.FAIL_BIZ, response != null && !StringUtils.isBlank(response.getMessage()) ? response.getMessage() : "更新城市失败", null);
            }
            JSONObject jsonObject = JSONObject.parseObject(response.getData().toString());
            broker.setCityId(jsonObject.getString("cityId"));
            broker.setCityName(jsonObject.getString("cityName"));
        }
        broker.setTownId(po.getTownId());
        broker.setTownName(po.getTownName());
        brokerRpt.save(broker);
        Long point = kerApi.getUserPoint(broker.getKerId());
        BrokerDTO brokerDTO = convert2DTO(broker, new BrokerDTO().setPoint(0L));
        brokerDTO.setPoint(point);
        return succ(brokerDTO);
    }

    @Override
    public ApiResponse<List<BannerDTO>> banners(SaasLoginToken loginToken) {
        Broker broker = brokerRpt.findById(loginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        if (StringUtils.isBlank(broker.getCityId())) {
            return succ();
        }
        List<Banner> banners = bannerRpt.findByCityId(broker.getCityId());
        return succ(convert2DTO(banners, new BannerDTO()));
    }

    @Override
    public ApiResponse<List<PointConfigDTO>> pointConfig(String category) {
        return succ(kerApi.getPointConfig(category));
    }

    @Override
    public ApiResponse<List<PointDetailDTO>> pointDetail(SaasLoginToken saasLoginToken, Integer page, Integer pageSize) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        return kerApi.getPointDetail(broker.getKerId(), page, pageSize);
    }

    @Override
    public String wechatCallback(String signature, String timestamp, String nonce, String echostr, HttpServletRequest request) {
        if (StringUtils.notBlank(echostr)) {
            return validWechatCallBack(signature, timestamp, nonce, echostr);
        }
        String xml = null;
        try {
            xml = getRequestBody(request);
        } catch (Exception e) {
            log.error("wechatCallback getRequestBody error = {}", e.getMessage());
            return "error";
        }
        log.info("wechatCallback xml = {}", xml);
        return handleWechatCallback(xml);
    }

    @Override
    public ApiResponse<Boolean> checkUser(String openId) {
        List<Broker> brokers = brokerRpt.findByOpenId(openId);
        return succ(brokers != null && !brokers.isEmpty());
    }

    public static String getRequestBody(HttpServletRequest request) throws IOException {
        // 使用 getReader() 获取请求体的 BufferedReader
        BufferedReader reader = request.getReader();
        StringWriter stringWriter = new StringWriter();
        PrintWriter writer = new PrintWriter(stringWriter);

        String line;
        while ((line = reader.readLine()) != null) {
            writer.println(line);
        }

        return stringWriter.toString();
    }

    private String validWechatCallBack(String signature, String timestamp, String nonce, String echostr) {
        String[] arr = new String[]{token, timestamp, nonce};
        Arrays.sort(arr);

        StringBuilder content = new StringBuilder();
        for (String str : arr) {
            content.append(str);
        }

        String sha1 = sha1(content.toString());
        if (sha1 != null && sha1.equals(signature)) {
            return echostr; // 返回echostr表示验证成功
        }

        return "error"; // 返回error表示验证失败
    }

    public String handleWechatCallback(String xml) {

            // 解析微信发送的XML
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            Document doc = null;
            try {
                DocumentBuilder builder = factory.newDocumentBuilder();
                doc = builder.parse(new InputSource(new StringReader(xml)));
            } catch (Exception e) {
                log.error(e.getMessage());
            }
            if (doc == null) {
                return "error";
            }
            // 获取根元素
            Element root = doc.getDocumentElement();

            // 获取各个字段的值
            String event = root.getElementsByTagName("Event").item(0).getTextContent();
            String openid = root.getElementsByTagName("FromUserName").item(0).getTextContent();
            String toUser = root.getElementsByTagName("ToUserName").item(0).getTextContent();
            String eventKey = root.getElementsByTagName("EventKey").item(0).getTextContent();
            log.info("wechatCallback event = {}, openId = {}, eventKey = {}", event, openid, eventKey);
            // 如果是关注事件
            if ("subscribe".equals(event) || "SCAN".equals(event)) {
                if (StringUtils.notBlank(eventKey)) {
                    // 提取临时二维码参数（EventKey 是二维码的参数）
                    log.info("handleWechatCallback task id = {}", eventKey);
                    Task task = taskRpt.findAndLockById(Long.parseLong(eventKey));
                    if (task != null) {
                        task.handleSendMessageOpenId(openid);
//                        String response = createResponseXml(openid.equals(task.getOpenId()), openid, toUser);
                        getBean(WechatService.class).sendMessageToWeChat(openid, openid.equals(task.getOpenId()) ? "扫码托管成功" : "您已关注成功，扫码托管已被其他微信账号绑定");
                    }
                }
            } else if ("unsubscribe".equals(event)) {
                List<Broker> brokers = brokerRpt.findByMessageOpenId(openid);
                brokers.forEach( t -> t.setMessageOpenId(null));
                List<Task> tasks = taskRpt.findByOpenIdAndStatus(openid, TaskDTO.Status.ING);
                tasks.forEach(Task::clearingMessageOpenId);
            }



        return "success";
    }

    private String createResponseXml(Boolean flag, String toUser, String fromUser) {
        StringBuilder responseXml = new StringBuilder();
        responseXml.append("<xml>");
        responseXml.append("<ToUserName><![CDATA[").append(toUser).append("]]></ToUserName>");
        responseXml.append("<FromUserName><![CDATA[gh_5133b43efafd]]></FromUserName>");
        responseXml.append("<CreateTime>").append(System.currentTimeMillis() / 1000).append("</CreateTime>");
        responseXml.append("<MsgType><![CDATA[text]]></MsgType>");
        responseXml.append("<Content><![CDATA[").append(flag ? "扫码托管成功" : "您已关注成功，扫码托管已被其他微信账号绑定").append("]]></Content>");
        responseXml.append("</xml>");
        return responseXml.toString();
    }

    // 生成SHA-1加密
    private static String sha1(String input) {
        try {
            MessageDigest sha1 = MessageDigest.getInstance("SHA-1");
            byte[] result = sha1.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : result) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @Valid
    @Operation(summary = "API_编辑经纪人")
    @PostMapping("/api/broker/edit")
    ApiResponse<BrokerDTO> edit4api(@NotNull(message = "经纪人信息不能为空") @RequestBody EditProjectPO po) {
        po.setPhone(po.getPhone());
        Broker broker = brokerRpt.findById(po.getId()).orElseGet(() -> new Broker(po).save());
        broker.edit(po);
        testSendMsg(po.getPhone());
        return succ(convert2DTO(broker, new BrokerDTO()));
    }

    private void testSendMsg(String phone) {
        MessageAPI messageAPI = gatewayEnabled ? gatewaySDK.feignClient(MessageAPI.class, messageUrl) : this.messageAPI;
        ApiResponse<List<MessageDTO>> response = messageAPI.sendMessage(new SendMessagePO(
                "saas.authCode",
                UUID.randomUUID().toString(),
                Map.of(
                        "phone", phone,
                        "authCode", "123456"
                )));
        log.info("edit4api response = {}", response);
    }


    private String generateRandomCode(int length) {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        // 生成随机验证码
        for (int i = 0; i < length; i++) {
            int digit = random.nextInt(10); // 生成一个 0 到 9 的数字
            code.append(digit);
        }
        return code.toString();
    }

    public Task getTask(String eventKey) {
        if (StringUtils.notBlank(eventKey)) {
            // 提取临时二维码参数（EventKey 是二维码的参数）
            Task task = taskRpt.findAndLockById(Long.parseLong(eventKey));
            if (task != null) {
                System.out.println(111);
            }
        }
        return null;
    }

    @Scheduled(cron = "${scheduler.tasks.createMonthlyMarketAnalysis}")
    public void createMonthlyMarketAnalysis() {
        LocalDate now = LocalDate.now();
        String year = String.valueOf(now.getYear());
        String month = String.valueOf(now.getMonthValue());
        List<Long> townIds = tweetRpt.findTownIdByTypeAndYearAndMonth(TweetDTO.Type.MONTHLY_REPORT, year, month);
        List<TownDTO> townDTOs = brokerRpt.findTown(townIds, PageRequest.of(0, 1));
        if (!CollectionUtils.isEmpty(townDTOs)) {
            TownDTO townDTO = townDTOs.get(0);
            Broker broker = brokerRpt.findById(adminBrokerId).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
            Tweet tweet = new Tweet(
                    TweetDTO.Type.MONTHLY_REPORT,
                    broker,
                    townDTO.getCityId(),
                    townDTO.getCityName(),
                    townDTO.getId(),
                    townDTO.getName(),
                    year, month, "");
            tweet = getBean(BrokerImpl.class).saveTweets(tweet);
            getBean(BrokerImpl.class).doBrokerTask(tweet);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Tweet saveTweets(Tweet tweets) {
        return tweetRpt.save(tweets);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void doBrokerTask(Tweet tweet) {
        tweet.createFlow();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ApiResponse<TweetDTO> flowCallBack(Long brokerTaskId, FlowDTO.Status status, String flowContent) {
        Tweet tweet = tweetRpt.findById(brokerTaskId).orElse(null);
        if (tweet == null || tweet.getStatus() != TweetDTO.Status.ING) {
            return succ();
        }
        tweet.flowComplete(status, flowContent);
        return succ(convert2DTO(tweet, new TweetDTO()));
    }
    
    public void createAnalysisOfIndustryPolicies(String cityId, String cityName, String content) {
        Broker broKer = brokerRpt.findById(adminBrokerId).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        Tweet tweet = new Tweet(TweetDTO.Type.INDUSTRY_ANALYST,
                broKer,
                cityId, cityName, content);
        tweet = getBean(BrokerImpl.class).saveTweets(tweet);
        getBean(BrokerImpl.class).doBrokerTask(tweet);
    }
}

