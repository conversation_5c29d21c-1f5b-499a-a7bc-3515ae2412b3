package com.dcai.aixg.config;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.ejuetc.consumer.api.community.CommunityAPI;
import com.ejuetc.consumer.api.delegation.DelegationAPI;
import com.ejuetc.consumer.gateway.CommunityGateway;
import com.ejuetc.saasapi.sdk.SaaSApiSDK;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "dcai.aixg.saasapi.feign-client")
public class SaaSAPIFeignClientConfig {

    private String consumerServerUrl;

    @Bean("delegationAPI_SaaSAPIFeign")
    public DelegationAPI delegationAPI() {
        return getBean(SaaSApiSDK.class).feignClient(DelegationAPI.class, consumerServerUrl);
    }

    @Bean("communityAPI_SaaSAPIFeign")
    public CommunityAPI communityAPI() {
        return getBean(SaaSApiSDK.class).feignClient(CommunityAPI.class, consumerServerUrl);
    }

    @Bean("communityGateway_SaaSAPIFeign")
    public CommunityGateway communityGateway() {
        return getBean(SaaSApiSDK.class).feignClient(CommunityGateway.class, consumerServerUrl);
    }
}