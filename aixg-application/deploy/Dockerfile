FROM image-store-registry.cn-shanghai.cr.aliyuncs.com/common/jdk17-skywalking8-playwright:v250806-1
LABEL maintainer="<EMAIL>"
ARG PUB_NAME
ARG PUB_PORT
ARG PUB_ENV
ENV TimeZone=Asia/Shanghai
ENV pub_env="$PUB_ENV"
ENV module_name="$PUB_NAME-application"
ENV java_applicaton_jar="$module_name.jar"

ADD ./$module_name/target/$java_applicaton_jar /$java_applicaton_jar
COPY ./$module_name/deploy/startup.release.sh /
COPY ./$module_name/deploy/agent-$pub_env.config /skywalking-agent/config/agent.config
VOLUME /tmp
EXPOSE $PUB_PORT
RUN ln -snf /usr/share/zoneinfo/$TimeZone /etc/localtime
RUN echo $TimeZone >/etc/timezone
RUN ["chmod", "+x", "/startup.release.sh"]
CMD [ "sh", "-c", "/startup.release.sh $pub_env $java_applicaton_jar" ]