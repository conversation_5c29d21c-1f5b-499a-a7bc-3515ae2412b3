# 区域价值分析提示词使用示例

本文档展示如何使用新创建的区域价值分析提示词文件。

## 示例1：使用完整版提示词

### 输入数据准备

**小区信息数据 (community_info):**
```json
{
  "name": "慧芝湖花园",
  "address": "平型关路1083弄",
  "cityName": "上海市",
  "districtName": "静安区",
  "detail": {
    "propertyFee": "2.7元/月/㎡",
    "greenRate": 0.45,
    "propertyName": "龙湖物业",
    "developerCorp": "嘉华(中国)投资有限公司"
  }
}
```

**周边配套数据 (around_data):**
```json
{
  "pois": [
    {
      "name": "上海市大宁国际小学",
      "distance": "254",
      "type": "科教文化服务;学校;小学"
    },
    {
      "name": "广中路平型关路(公交站)",
      "distance": "305",
      "address": "107路;547路;767路;79路;858路;862路;912路;944路"
    },
    {
      "name": "百联莘荟购物中心",
      "distance": "500",
      "type": "购物服务;商场;购物中心"
    }
  ]
}
```

### 使用方法

1. 复制 `区域价值分析_完整版_prom.md` 的内容
2. 替换模板变量：
   - `${community_info}` → 上述小区信息JSON
   - `${around_data}` → 上述周边配套JSON
3. 输入AI对话系统

### 预期输出示例

```markdown
## 区域价值
作为上海静安区大宁板块的品质住宅标杆，慧芝湖花园坐拥都市核心配套资源，45%绿化率营造出宜居生态环境。项目由嘉华(中国)投资有限公司开发，龙湖物业管理（物业费2.7元/月/㎡），完美融合都市便利与居住品质。

区域核心价值体现于：
- **教育资源聚集**：步行3分钟直达大宁国际小学，优质教育触手可及
- **交通网络完善**：8条公交线路交汇，出行便利四通八达
- **商业配套成熟**：百联莘荟购物中心500米范围内，品质生活近在咫尺
- **生态宜居环境**：45%绿化覆盖率，都市中的绿色家园

## 交通网络
- 公交覆盖：广中路平型关路站汇聚107路、547路、767路等8条线路，织密出行网络

## 生活配套
- 商业矩阵：
  - 百联莘荟购物中心（综合商业体）

## 教育资源
- 全龄教育链：
  - 上海市大宁国际小学（优质公立）

## 小区品质
- 生态美学：
  - 45%森林级绿化覆盖率
  - 营造都市绿洲般的居住环境
- 服务标准：
  - 龙湖物业专业管理
  - 2.7元/月/㎡的合理物业费用
```

## 示例2：使用FreeMarker模板

### Java代码示例

```java
@Service
public class RegionAnalysisService {
    
    @Autowired
    private Configuration freemarkerConfig;
    
    public String generateRegionAnalysis(String communityInfo, String aroundData) {
        try {
            // 准备数据模型
            Map<String, Object> dataModel = new HashMap<>();
            dataModel.put("community_info", communityInfo);
            dataModel.put("around_data", aroundData);
            
            // 渲染系统提示词
            Template sysTemplate = freemarkerConfig.getTemplate("sys_prom_template.ftl");
            String systemPrompt = FreeMarkerTemplateUtils.processTemplateIntoString(sysTemplate, dataModel);
            
            // 渲染用户提示词
            Template userTemplate = freemarkerConfig.getTemplate("user_prom_template.ftl");
            String userPrompt = FreeMarkerTemplateUtils.processTemplateIntoString(userTemplate, dataModel);
            
            // 调用AI生成内容
            return callAIService(systemPrompt, userPrompt);
            
        } catch (Exception e) {
            log.error("生成区域价值分析失败", e);
            throw new RuntimeException("生成区域价值分析失败", e);
        }
    }
    
    private String callAIService(String systemPrompt, String userPrompt) {
        // 调用AI服务的具体实现
        // 这里可以集成OpenAI、Claude等AI服务
        return aiService.chat(systemPrompt, userPrompt);
    }
}
```

### 配置示例

```java
@Configuration
public class FreeMarkerConfig {
    
    @Bean
    public Configuration freemarkerConfiguration() {
        Configuration config = new Configuration(Configuration.VERSION_2_3_31);
        config.setClassForTemplateLoading(this.getClass(), "/freemark_template/");
        config.setDefaultEncoding("UTF-8");
        config.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        return config;
    }
}
```

## 示例3：使用优化版提示词

### 系统提示词
使用 `区域价值分析_prom_v2.md` 的system_prompt部分作为系统提示词。

### 用户消息
```
请基于以下数据生成区域价值分析文档：

小区信息：
{
  "name": "慧芝湖花园",
  "address": "平型关路1083弄",
  "cityName": "上海市",
  "districtName": "静安区",
  "detail": {
    "propertyFee": "2.7元/月/㎡",
    "greenRate": 0.45,
    "propertyName": "龙湖物业"
  }
}

周边配套：
{
  "pois": [
    {
      "name": "上海市大宁国际小学",
      "distance": "254",
      "type": "科教文化服务;学校;小学"
    },
    {
      "name": "百联莘荟购物中心",
      "distance": "500",
      "type": "购物服务;商场;购物中心"
    }
  ]
}
```

## 质量检查要点

### 1. 数据准确性检查
- [ ] 所有配套设施名称与输入数据一致
- [ ] 距离信息准确无误
- [ ] 小区基本信息正确

### 2. 表达风格检查
- [ ] 使用专业的房地产行业术语
- [ ] 价值导向的表达方式
- [ ] 生动的配套描述和价值标签

### 3. 结构完整性检查
- [ ] 包含所有必要章节
- [ ] 章节内容充实，不是简单罗列
- [ ] Markdown格式规范

### 4. 价值分析深度检查
- [ ] 深度挖掘配套设施的价值意义
- [ ] 站在购房者角度分析
- [ ] 突出区域独特优势

## 常见问题解决

### Q1: 生成的内容过于简单怎么办？
**A**: 检查输入数据是否完整，确保包含足够的配套信息。可以在提示词中强调"深度分析"和"充实内容"。

### Q2: 表达风格不够专业怎么办？
**A**: 在用户提示词中添加更多样例文件的表达风格参考，强调使用专业术语。

### Q3: 如何确保数据不被虚构？
**A**: 提示词中已包含严格的数据准确性要求，强调"严禁数据造假"和"仅从输入数据提取"。

### Q4: 如何适配不同城市的分析需求？
**A**: 可以根据不同城市的特点调整价值表达模式和配套分类方式，但核心框架保持不变。

## 扩展建议

### 1. 个性化定制
- 根据不同客户群体调整分析重点
- 针对不同价位段的项目调整表达风格
- 考虑区域特色和文化背景

### 2. 数据丰富化
- 增加更多类型的配套数据
- 添加价格、评分等量化指标
- 包含历史发展和未来规划信息

### 3. 多媒体支持
- 支持图片和地图信息
- 添加视频和VR展示链接
- 集成实时数据更新

### 4. 智能优化
- 基于用户反馈优化提示词
- 使用A/B测试验证效果
- 持续迭代和改进
