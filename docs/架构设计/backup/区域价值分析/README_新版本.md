# 区域价值分析 AI提示词文档

本目录包含用于生成区域价值分析报告的AI提示词文件和相关资源。

## 文件说明

### 1. 原有文件
- **区域价值分析_prom.md**: 原有的AI提示词文件，包含完整的数据转换规则
- **区域价值_家辉.md**: 样例文件，展示专业区域价值分析的格式和表达风格
- **区域价值_cric.md**: 另一个样例文件，提供不同的分析视角

### 2. 新创建的提示词文件（基于样例文件优化）

#### 2.1 区域价值分析_prom_v2.md
- **用途**: 基于样例文件优化的完整AI提示词文件
- **特点**: 深度参考`区域价值_家辉.md`的表达风格和结构
- **优势**: 更专业的价值表达模式，更贴近房地产行业标准
- **适用场景**: 可直接作为AI对话的提示词使用

#### 2.2 区域价值分析_完整版_prom.md  
- **用途**: 整合版AI提示词文件，包含system_prompt和user_prompt
- **特点**: 完整的提示词结构，可直接用于AI对话
- **格式**: 使用标准的分隔符格式，便于系统解析
- **适用场景**: 直接复制使用，无需额外配置

#### 2.3 FreeMarker模板文件
- **sys_prom_template.ftl**: 系统提示词模板，符合项目技术架构
- **user_prom_template.ftl**: 用户提示词模板，支持动态数据填充
- **特点**: 使用FreeMarker语法，支持模板变量和条件判断
- **适用场景**: 集成到Java应用中，通过FreeMarker引擎生成提示词

### 3. 样例和数据文件
- **around.json**: 周边配套数据示例
- **community.json**: 小区信息数据示例

## 核心改进点

### 1. 专业表达风格
新版本提示词深度参考了`区域价值_家辉.md`样例文件的专业表达风格：

- **价值标签化**: 为区域特色创建专业标签（如"全球枢纽地位"、"精英圈层聚合"）
- **场景化描述**: 将配套优势转化为生活场景描述
- **量化价值表达**: 将距离、数量等数据转化为价值表述

### 2. 结构化组织
按照专业房地产分析报告的标准结构组织内容：

1. **区域价值** - 核心价值概述和定位
2. **交通网络** - 轨交动脉、路网中枢、公交覆盖
3. **生活配套** - 医疗旗舰、商业矩阵、休闲图鉴
4. **教育资源** - 全龄教育链、特色优势
5. **小区品质** - 生态美学、建筑基因、服务标准

### 3. 价值导向分析
- 突出区域的投资价值和生活价值
- 将基础配套信息转化为专业的价值表述
- 站在购房者角度分析区域价值和投资潜力

## 使用方法

### 方法一：使用完整版提示词
```
1. 复制 `区域价值分析_完整版_prom.md` 的内容
2. 将 ${community_info} 替换为实际的小区信息JSON数据
3. 将 ${around_data} 替换为实际的周边配套JSON数据
4. 直接输入AI对话系统使用
```

### 方法二：使用FreeMarker模板
```java
// 准备数据模型
Map<String, Object> dataModel = new HashMap<>();
dataModel.put("community_info", communityInfoJson);
dataModel.put("around_data", aroundDataJson);

// 渲染系统提示词
Template sysTemplate = freemarkerConfig.getTemplate("sys_prom_template.ftl");
String systemPrompt = FreeMarkerTemplateUtils.processTemplateIntoString(sysTemplate, dataModel);

// 渲染用户提示词
Template userTemplate = freemarkerConfig.getTemplate("user_prom_template.ftl");
String userPrompt = FreeMarkerTemplateUtils.processTemplateIntoString(userTemplate, dataModel);
```

### 方法三：使用优化版提示词
```
1. 使用 `区域价值分析_prom_v2.md` 作为基础提示词
2. 在用户消息中提供具体的JSON数据
3. AI将按照专业标准生成区域价值分析文档
```

## 输出文档特色

### 1. 专业价值表达
- "作为上海静安区大宁板块的品质住宅标杆"
- "坐拥城市核心配套资源，45%绿化率营造宜居环境"
- "完美融合都市便利与居住品质"

### 2. 配套价值化描述
- "轨交动脉：步行5分钟直达地铁站，畅达全城"
- "医疗旗舰：三甲医院环伺，健康保障无忧"
- "商业矩阵：大型购物中心近在咫尺，品质生活触手可及"

### 3. 数据驱动分析
- 基于真实配套数据进行价值分析
- 将距离、数量等基础数据转化为便利性表述
- 突出区域独特优势和稀缺性

## 质量保证

### 1. 数据准确性
- 严格基于输入的JSON数据，绝不虚构信息
- 保持距离、名称、地址等信息的精确性
- 对缺失数据进行合理处理

### 2. 专业水准
- 达到专业房地产分析报告的质量标准
- 使用房地产行业的专业术语和表达方式
- 深度挖掘配套设施的价值意义

### 3. 客户导向
- 站在购房者角度分析区域价值
- 突出对不同客户群体的价值意义
- 提供实用的投资和置业参考

## 技术特点

### 1. 模板化设计
- 支持FreeMarker模板引擎
- 便于集成到Java应用中
- 支持动态数据填充和条件判断

### 2. 结构化输出
- 标准的Markdown格式输出
- 清晰的章节结构和层次
- 便于后续处理和展示

### 3. 可扩展性
- 模块化的提示词设计
- 易于根据需求调整和优化
- 支持不同类型的区域分析需求

## 注意事项

1. **数据完整性**: 确保输入的JSON数据结构完整，包含必要的配套信息
2. **格式规范**: 严格按照JSON格式提供数据，避免格式错误
3. **内容真实性**: 所有生成内容都基于输入数据，不会添加虚构信息
4. **专业表达**: 输出内容使用专业的房地产行业表达方式
5. **价值导向**: 重点突出区域的投资价值和生活价值

## 更新日志

- **2025-01-24**: 创建基于样例文件的新版本提示词
- **2025-01-24**: 添加FreeMarker模板支持
- **2025-01-24**: 优化专业表达风格和价值分析框架
