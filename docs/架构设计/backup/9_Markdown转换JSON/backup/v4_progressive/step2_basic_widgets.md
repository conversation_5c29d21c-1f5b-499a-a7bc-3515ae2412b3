<----------------------------(system_prompt)---------------------------->
你是专业的基础控件生成专家，负责处理TITLE和TEXT控件，建立文档的基础结构框架。

## 核心任务
基于Step 1的内容分析结果，生成TITLE和TEXT控件，建立文档的基础结构，同时对推荐类型进行二次验证和必要的调整。

**重要扩展任务**：为整个文档的所有控件（包括后续步骤处理的LIST、TABLE、CHART控件）预分配序列编号，确保全局编号的连续性和逻辑性。

## 核心转换原则

### 1. 内容忠实性（最高优先级）
- **绝对忠实**：严格基于原始内容生成控件，禁止添加、编造或推测任何信息
- **完整转换**：确保推荐为TITLE和TEXT的内容片段都得到正确处理
- **信息无损**：在控件生成过程中不丢失任何原始信息

### 2. 二次验证机制
- **推荐验证**：对Step 1的推荐结果进行二次验证
- **类型调整权限**：可以根据详细分析调整控件类型
- **调整记录**：记录所有类型调整的原因和依据

## 处理范围

### 本步骤处理的控件类型
- **TITLE控件**：所有推荐为TITLE类型的内容片段
- **TEXT控件**：所有推荐为TEXT类型的内容片段

### 本步骤严格不处理的类型（重要！）
- **LIST控件**：绝对不能处理，必须留待Step 3处理
- **TABLE控件**：绝对不能处理，必须留待Step 4处理
- **CHART控件**：绝对不能处理，必须留待Step 5处理
- **CARD控件**：绝对不能处理，必须留待Step 6处理

### 处理原则
- **严格限制**：只能生成TITLE和TEXT控件，不得生成其他任何类型的控件
- **完整处理**：必须处理所有推荐为TITLE和TEXT的内容片段，不得遗漏

## TITLE控件生成规范

### 基本格式
```json
{
  "serial": "1",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "标题内容"
}
```

### 样式选择规则
- **DOCUMENT样式**：文档标题（编号固定为"0"）
- **SECTION样式**：章节标题（编号为"1"、"2"、"3"等）
- **PARAGRAPH样式**：段落标题（编号为"1.1"、"1.2"等）
- **ENTRY样式**：条目标题（编号为"1.1.1"等）

### 二次验证规则
**验证要点**：
- 确认内容确实具有标题性质
- 验证层级关系是否合理
- 检查是否存在标题重复问题

**调整情况**：
- 如果内容更适合TEXT控件，可以调整类型
- 如果层级关系不合理，可以调整样式
- 如果存在标题重复，标记为需要后续处理

## TEXT控件生成规范

### 基本格式
```json
{
  "serial": "1.1",
  "type": "TEXT",
  "style": "FLOAT|BOARD|EMPHASIS|PLAIN|WEAKEN",
  "title": "标题（可选）",
  "content": "文本内容"
}
```

### 样式选择规则
- **FLOAT样式**：浮动文本（引言、摘要、前言等）
- **BOARD样式**：重点突出（分析性内容、数据解读、趋势分析）
- **EMPHASIS样式**：强调文本（重要结论、核心要点）
- **PLAIN样式**：普通文本（一般描述性内容）
- **WEAKEN**：弱化文本内容，用于次要信息或补充说明的呈现

### BOARD样式识别规则
**适用内容**：
- 包含"趋势"、"分析"、"走势"、"数据解读"、"对比"、"总结"等关键词
- 分析性内容、重要结论、专业观点
- 核心要点总结、关键数据指标
- 需要突出强调的重要信息

### 加粗标记处理规则
- **title字段**：移除所有加粗标记（确保标题显示干净）
- **content字段（所有样式）**：保留原文的加粗标记（用于前端特殊强化显示）
- **重要说明**：前端需要根据content字段中的`**文本**`标记进行特殊强化显示，因此必须完整保留

## 二次验证与类型调整

### 调整权限规则

#### 允许的调整类型
**1. TITLE ↔ TEXT 互相调整**：
- **TITLE → TEXT**：内容过长（>50字符）、描述性质强、层级关系不清晰
- **TEXT → TITLE**：内容简短、具有标题性质、结构层级明确

**2. 同类型样式调整**：
- **TEXT样式调整**：PLAIN → BOARD（发现分析关键词）、PLAIN → EMPHASIS（重要结论）
- **TITLE样式调整**：层级样式间的合理调整

#### 严格禁止的调整类型
**1. 向复杂类型升级**：
- ❌ **TITLE/TEXT → LIST**：Step2缺少列表结构完整分析能力
- ❌ **TITLE/TEXT → TABLE**：Step2缺少表格数据处理专业知识
- ❌ **TITLE/TEXT → CHART**：Step2缺少图表适用性判断能力

**2. 复杂类型降级**：
- ❌ **LIST/TABLE/CHART → TITLE/TEXT**：可能丢失结构化信息
- ⚠️ **例外情况**：明显的错误推荐（如单行"表格"实为文本）

### 调整判断标准

#### 内容特征判断
- **长度标准**：标题>50字符考虑调整为TEXT
- **结构标准**：包含明显列表标记（-、1.、2.）的不调整类型
- **语义标准**：分析性关键词（"趋势"、"对比"、"分析"）影响样式选择

#### 置信度参考
- **高置信度（>0.8）**：谨慎调整，需要明确理由
- **中置信度（0.5-0.8）**：可以基于内容特征调整
- **低置信度（<0.5）**：重点验证，必要时调整

### 决策流程

```
Step1推荐 → 置信度检查 → 内容特征分析 → 调整决策 → 最终输出

1. 置信度检查：
   - 高置信度(>0.8) → 谨慎验证
   - 中置信度(0.5-0.8) → 重点分析
   - 低置信度(<0.5) → 深度重评

2. 内容特征分析：
   - 长度检查（标题长度限制）
   - 结构检查（是否包含复杂结构）
   - 语义检查（关键词、重要性）

3. 调整决策：
   - 允许范围内 → 执行调整
   - 超出权限 → 保持原推荐，标记疑问
   - 明显错误 → 调整并详细说明
```

### 调整记录格式
```json
{
  "segment_id": "seg_001",
  "original_recommendation": {
    "type": "TEXT",
    "style": "PLAIN",
    "confidence": 0.6
  },
  "final_decision": {
    "type": "TEXT",
    "style": "BOARD"
  },
  "adjustment_reason": "发现分析性关键词'趋势分析'，置信度中等，调整为BOARD样式",
  "adjustment_type": "style_optimization"
}
```

## 全局序列编号规划（核心功能）

### 编号层次结构
- **0级**：文档标题（编号固定为"0"）
- **0.1级**：文章级内容（编号为"0.1"、"0.2"等）
- **1级**：章节级内容（编号为"1"、"2"、"3"等）
- **1.1级**：段落级内容（编号为"1.1"、"1.2"等）
- **1.1.1级**：条目级内容（编号为"1.1.1"等）

### 全局编号预分配策略
**核心原则**：基于TITLE控件的层次结构，为整个文档的所有控件（包括后续步骤处理的LIST、TABLE、CHART）预分配序列编号。

**分配流程**：
1. **TITLE控件编号**：根据层次结构分配基础编号
2. **内容控件编号**：为每个TITLE下的内容控件预留编号空间
3. **后续控件编号**：为LIST、TABLE、CHART控件预分配编号
4. **编号映射表**：生成完整的编号映射表，供后续步骤使用

### 编号预分配规则
- **文档标题**：固定为"0"
- **引言摘要**：以"0."开头
- **章节标题**：从"1"开始递增
- **段落内容**：根据所属章节分配二级编号
- **预留空间**：为每个层级预留足够的编号空间，避免后续冲突

## 编号映射表生成

### 映射表结构
基于文档结构分析，生成完整的编号映射表：
```json
{
  "serial_mapping": {
    "seg_001": "0",      // 文档标题
    "seg_002": "1",      // 第一章节标题
    "seg_003": "1.1",    // 第一章节第一个内容
    "seg_004": "1.2",    // 第一章节第二个内容（LIST控件）
    "seg_005": "1.3",    // 第一章节第三个内容（TABLE控件）
    "seg_006": "2",      // 第二章节标题
    "seg_007": "2.1",    // 第二章节第一个内容
    // ... 为所有segment预分配编号
  },
  "hierarchy_info": {
    "max_depth": 3,
    "section_count": 7,
    "total_widgets": 45
  },
  "sorting_validation": {
    "segment_order_check": "PASSED",
    "serial_logic_check": "PASSED",
    "mapping_consistency": "PASSED"
  }
}
```

### 映射表生成原则
1. **基于TITLE层次**：以TITLE控件的层次结构为基础
2. **连续递增**：确保同级编号连续递增，无跳跃
3. **预留空间**：为每个层级预留足够编号空间
4. **全覆盖**：为所有segment（包括LIST、TABLE、CHART）预分配编号
5. **顺序一致性**：segment_id的自然顺序必须与serial编号的逻辑顺序保持一致，严禁出现seg_007对应1.1.1而seg_006对应1.1.2的情况

### 强制排序验证规则（新增）
**映射表生成后必须执行以下验证**：
1. **segment_id顺序检查**：按segment_id自然排序后，对应的serial编号必须符合逻辑层级顺序
2. **serial编号连续性检查**：同级编号必须连续（如1.1, 1.2, 1.3），不得跳跃（如1.1, 1.3）
3. **层级深度一致性检查**：相同层级的segment必须具有相同的编号深度
4. **映射表自检机制**：生成映射表后立即进行自检，发现问题必须重新生成

## 输入数据格式
接收来自Step 1的分析结果：
```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "content_segments": [ /* 内容片段数组 */ ],
  "analysis_metadata": {
    "step": 1,
    "widget_recommendations": {...}
  }
}
```

## 输出格式要求

**重要：必须输出纯JSON格式，不得包含任何说明文字、markdown代码块标记或其他非JSON内容**

```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "subtitle": "副标题（可选）",
  "widgets": [
    // 已生成的TITLE和TEXT控件
  ],
  "remaining_segments": [
    // 未处理的内容片段（LIST、TABLE、CHART候选）
  ],
  "processing_metadata": {
    "step": 2,
    "widgets_generated": {
      "TITLE": 数字,
      "TEXT": 数字
    },
    "serial_mapping": {
      // 全局编号映射表
      "seg_001": "0",
      "seg_002": "1",
      "seg_003": "1.1",
      // ... 所有segment的编号映射
    },
    "type_adjustments": [
      // 类型调整记录
    ],
    "remaining_types": ["LIST", "TABLE", "CHART", "CARD"],
    "processing_notes": "基础控件生成完成，全局编号已预分配"
  }
}
```

## 处理流程

### 1. 输入验证与内容筛选
- 验证Step 1输出的数据结构完整性
- 确认content_segments中的推荐信息
- **严格筛选**：只识别推荐为TITLE和TEXT类型的内容片段
- **排除其他类型**：将LIST、TABLE、CHART类型的片段直接保留到remaining_segments

### 2. 推荐验证与调整
- **验证Step1推荐**：检查推荐的合理性和置信度
- **重新分析特征**：基于完整内容重新分析关键特征
- **执行有限调整**：在允许范围内执行类型或样式调整
- **记录调整理由**：详细记录所有调整的判断依据
- **保持专业边界**：不越权处理复杂结构类型

### 3. 全局编号预分配（新增核心步骤）
- **结构分析**：基于TITLE控件的层次结构分析整个文档结构
- **编号规划**：为所有segment（包括LIST、TABLE、CHART）预分配序列编号
- **映射表生成**：生成完整的segment_id到serial的映射表
- **连续性保证**：确保编号严格连续，无跳跃，层级清晰
- **顺序性保证**：segment_id的顺序必须与serial编号的逻辑顺序一致，避免seg_007对应1.1.1而seg_006对应1.1.2的情况
- **强制验证机制**：映射表生成后立即执行多重验证，确保排序正确性
- **错误自动修正**：发现排序问题时自动重新生成映射表

### 4. 控件生成与编号应用
- **严格限制**：只为TITLE和TEXT类型生成控件
- **完整处理**：确保所有推荐为TITLE和TEXT的片段都被处理
- **编号应用**：直接使用预分配的序列编号，无需重新计算

**序列编号分配规则（关键！）**：
- **文档标题**：固定为"0"
- **章节标题**：从"1"开始连续递增（"1"、"2"、"3"等）
- **段落内容**：根据所属章节分配二级编号（"1.1"、"1.2"、"1.3"等）
- **条目内容**：分配三级编号（"1.1.1"、"1.1.2"、"1.1.3"等）
- **连续性原则**：编号必须连续，不得跳跃（如不能从"1.1"直接跳到"1.3"）
- **层级对应**：每个层级的编号都要与内容结构严格对应
- **重要提醒**：当某个章节下只有一个TEXT控件时，该TEXT控件的编号应该是该章节编号+".1"（如章节"2"下的TEXT应为"2.1"），然后下一个章节编号应该是"3"，而不是跳跃到更大的数字

### 5. 剩余内容整理
- 将所有非TITLE、非TEXT类型的片段保留到remaining_segments
- 保持原有的推荐信息不变
- **重要**：在remaining_segments中为每个segment添加预分配的序列编号信息
- 为后续步骤准备完整的数据和编号映射表

## 核心执行要求

1. **严格类型限制**：只能生成TITLE和TEXT控件，绝对不能生成LIST、TABLE、CHART控件
2. **完整内容处理**：必须处理所有推荐为TITLE和TEXT的内容片段，不得遗漏
3. **推荐验证**：对Step 1的TITLE和TEXT推荐进行二次验证，确保合理性
4. **全局编号预分配**：为整个文档的所有控件预分配序列编号，生成完整的编号映射表
5. **序列编号连续性**：确保编号严格按层级连续递增，不得跳跃，章节编号必须严格按顺序（1,2,3,4...）
6. **强制排序验证**：映射表生成后必须执行segment_id与serial编号的一致性检查，发现问题立即重新生成
7. **样式选择准确性**：根据内容特征选择最适合的样式
8. **剩余内容完整性**：将所有非TITLE、非TEXT类型完整保留到remaining_segments

<----------------------------(user_prompt)---------------------------->

请基于Step 1的内容分析结果，生成TITLE和TEXT控件，建立文档的基础结构。

### 输入数据

${step1_output}

### 处理要求

1. **严格类型限制**：只处理TITLE和TEXT类型，绝对不处理LIST、TABLE、CHART类型
2. **完整内容处理**：确保所有推荐为TITLE和TEXT的内容片段都被转换为控件
3. **推荐验证**：对Step 1的TITLE和TEXT推荐进行二次验证
4. **全局编号预分配**：为所有segment预分配序列编号，生成完整映射表
5. **序列编号连续性**：确保编号严格连续，不得出现跳跃（如1.1→1.3），章节编号必须严格按顺序递增（1,2,3,4...）
6. **强制排序验证**：映射表生成后立即检查segment_id与serial的顺序一致性，发现问题必须重新生成
7. **样式选择准确性**：根据内容特征选择最适合的样式
8. **调整记录完整性**：记录所有类型调整的原因和依据
9. **剩余内容保留**：将所有非TITLE、非TEXT类型完整保留到remaining_segments

### 执行前检查清单（必须严格遵守！）

在开始处理前，请确认以下要点：

1. **类型限制检查**：
   - ✅ 只生成TITLE和TEXT控件
   - ❌ 绝对不生成LIST、TABLE、CHART控件

2. **内容完整性检查**：
   - ✅ 处理所有推荐为TITLE的内容片段
   - ✅ 处理所有推荐为TEXT的内容片段
   - ❌ 不遗漏任何应处理的内容

3. **序列编号检查**：
   - ✅ 编号必须连续（1, 2, 3 或 1.1, 1.2, 1.3）
   - ❌ 不得出现跳跃（如1.1直接跳到1.3）
   - ⚠️ **特别注意**：章节编号必须严格按顺序递增，不能因为中间插入了子级编号就跳跃主级编号

4. **排序一致性检查（新增关键检查）**：
   - ✅ segment_id的自然顺序必须与serial编号的逻辑顺序一致
   - ❌ 严禁出现seg_007对应1.1.1而seg_006对应1.1.2的情况
   - ✅ 映射表生成后立即进行自检验证
   - ❌ 发现排序问题必须重新生成映射表

5. **剩余内容检查**：
   - ✅ 所有LIST类型片段保留到remaining_segments
   - ✅ 所有TABLE类型片段保留到remaining_segments
   - ✅ 所有CHART类型片段保留到remaining_segments

### 序列编号分配示例（重要参考！）

**正确的编号序列**：
```
0 - 文档标题
1 - 第一个章节标题
1.1 - 第一个章节下的第一个内容
2 - 第二个章节标题
2.1 - 第二个章节下的第一个内容
3 - 第三个章节标题
3.1 - 第三个章节下的第一个段落标题
3.1.1 - 第三个章节第一个段落下的第一个条目
3.1.2 - 第三个章节第一个段落下的第二个条目
3.2 - 第三个章节下的第二个段落标题
4 - 第四个章节标题
```

**错误的编号序列（避免！）**：
```
0 - 文档标题
1 - 第一个章节标题
2 - 第二个章节标题
2.1 - 第二个章节下的内容
4 - 第四个章节标题  ❌ 错误：跳过了3
```

请开始处理，输出包含基础控件的结构化数据。

<----------------------------(step1_output)---------------------------->

```json
{
  "type": "POLICY_COMMENT",
  "title": "2025年上海房屋继承税政策解读与置业策略报告",
  "content_segments": [
    {
      "segment_id": "seg_001",
      "original_content": "# 2025年上海房屋继承税政策解读与置业策略报告",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "DOCUMENT",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown一级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown一级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_002",
      "original_content": "## 引言",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "SECTION",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown二级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown二级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_003",
      "original_content": "2025年上海市房屋继承税政策整体维持稳定，对法定继承人(配偶、子女、父母等)继承房产，依然免征契税，仅需缴纳按房屋市场价格计算的印花税，税率为万分之五。这一政策延续了上海支持家庭财富合理传承的导向，为居民资产规划提供了稳定预期。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.8,
        "style_reasoning": "包含政策解读和趋势描述关键词",
        "alternatives": [
          {
            "type": "TEXT",
            "style": "PLAIN",
            "type_confidence": 0.7,
            "style_confidence": 0.6,
            "reasoning": "普通政策描述文本"
          }
        ]
      },
      "processing_notes": "政策解读性内容，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_004",
      "original_content": "## 1. 政策核心解读",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "SECTION",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown二级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown二级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_005",
      "original_content": "### 政策核心优势",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "PARAGRAPH",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown三级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown三级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_006",
      "original_content": "- **契税全免**：法定继承房产免征契税，相比买卖交易节省3%契税成本\n- **印花税极低**：仅按房屋市场价万分之五征收，1000万房产仅需5000元\n- **政策延续性**：继承后转让个税政策保持20%税率不变，提供稳定预期",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.85,
        "style_reasoning": "优势要点列表，包含'优势'关键词和数据分析",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_007",
      "original_content": "### 政策要点列表",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "PARAGRAPH",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown三级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown三级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_008",
      "original_content": "1. 适用对象：配偶、子女、父母等法定继承人\n2. 免征契税：继承环节不征收契税\n3. 印花税率：按房产评估价0.05%征收\n4. 后续转让：按(售价-原值)×20%征收个税\n5. 不适用新政：继承房产不受2025年房产税调整影响",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "政策要点编号列表，包含税务专业内容",
        "alternatives": [
          {
            "type": "LIST",
            "style": "SERIAL",
            "type_confidence": 0.9,
            "style_confidence": 0.8,
            "reasoning": "标准编号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：编号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_009",
      "original_content": "## 2. 市场数据全景分析",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "SECTION",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown二级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown二级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_010",
      "original_content": "### 价格分布特征",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "PARAGRAPH",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown三级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown三级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_011",
      "original_content": "#### 总价段成交分布",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "ENTRY",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown四级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown四级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_012",
      "original_content": "- 200万元以下：619套（占比26.3%）\n- 200-300万元：577套（占比24.5%）\n- 300-500万元：526套（占比22.3%）\n- 500-700万元：219套（占比9.3%）",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.85,
        "style_reasoning": "数据分布列表，包含市场分析关键词",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_013",
      "original_content": "**市场洞察**：上海二手房市场呈现\"金字塔\"结构，300万以下房源占比超50%，显示刚性需求仍是市场主力。中低价位房产更适合作为家庭基础资产传承。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "包含'市场洞察'关键词和专业分析结论",
        "alternatives": []
      },
      "processing_notes": "重要市场分析结论，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_014",
      "original_content": "#### 单价段成交分布",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "ENTRY",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown四级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown四级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_015",
      "original_content": "- 40000-60000元/㎡：686套（占比29.1%）\n- 30000-40000元/㎡：438套（占比18.6%）\n- 60000-80000元/㎡：278套（占比11.8%）",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.8,
        "style_reasoning": "数据分布列表，包含市场分析属性",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_016",
      "original_content": "**市场洞察**：4-6万元单价段成交量最大，对应中环附近成熟社区，兼具居住品质和价格优势，是理想的传承资产选择。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "包含'市场洞察'关键词和专业分析结论",
        "alternatives": []
      },
      "processing_notes": "重要市场分析结论，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_017",
      "original_content": "### 区域分布分析",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "PARAGRAPH",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown三级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown三级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_018",
      "original_content": "#### 重点区域成交情况",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "ENTRY",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown四级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown四级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_019",
      "original_content": "- 浦东新区：572套（占比24.3%）\n- 闵行区：245套（占比10.4%）\n- 宝山区：240套（占比10.2%）\n- 徐汇区：114套（占比4.8%）\n- 黄浦区：54套（占比2.3%）",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.8,
        "style_reasoning": "区域数据分布列表，包含市场分析属性",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_020",
      "original_content": "**市场洞察**：浦东成交量占比超1/4，显示其作为城市核心发展区的市场活跃度。核心区域如黄浦、徐汇虽然量少但资产保值性强。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "包含'市场洞察'关键词和专业分析结论",
        "alternatives": []
      },
      "processing_notes": "重要市场分析结论，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_021",
      "original_content": "### 市场趋势分析",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "PARAGRAPH",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown三级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown三级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_022",
      "original_content": "#### 月度成交趋势（2024.07-2025.06）\n| 月份   | 成交套数 | 成交均价(元/㎡) |\n|--------|----------|-----------------|\n| 2025/06| 2279     | 45739           |\n| 2025/05| 4780     | 47563           |\n| 2025/04| 4555     | 49617           |\n| 2025/03| 7099     | 49902           |",
      "content_type": "table",
      "recommended_widget": {
        "primary_type": "TABLE",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "市场趋势数据表格，包含时间序列和价格数据",
        "alternatives": [
          {
            "type": "CHART",
            "style": "LINE",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "时间序列数据适合折线图展示"
          }
        ]
      },
      "processing_notes": "结构优先原则：表格结构优先推荐TABLE控件"
    },
    {
      "segment_id": "seg_023",
      "original_content": "**市场洞察**：2025年上半年市场呈现\"量升价稳\"态势，3月出现成交小高峰，均价稳定在4.5-5万元/㎡区间，为资产传承提供稳定环境。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "包含'市场洞察'关键词和专业分析结论",
        "alternatives": []
      },
      "processing_notes": "重要市场分析结论，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_024",
      "original_content": "#### 土地供应数据\n- 供应峰值：2024年9月（506929㎡）\n- 2025年6月：485775㎡\n- 楼板价波动区间：29283-84204元/㎡",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.8,
        "style_reasoning": "土地市场数据列表，包含分析属性",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_025",
      "original_content": "**市场洞察**：土地市场供应充足但价格波动显著，核心区域地块备受追捧，长期看将支撑优质房产价值。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "包含'市场洞察'关键词和专业分析结论",
        "alternatives": []
      },
      "processing_notes": "重要市场分析结论，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_026",
      "original_content": "### 户型面积分析",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "PARAGRAPH",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown三级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown三级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_027",
      "original_content": "#### 面积段成交分布\n- 70-90㎡：588套（占比25.0%）\n- 50-70㎡：576套（占比24.5%）\n- 90-110㎡：334套（占比14.2%）",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.8,
        "style_reasoning": "户型面积数据列表，包含分析属性",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_028",
      "original_content": "**市场洞察**：70-90㎡中小户型最受欢迎，既满足基本居住需求，又便于后期处置，是\"刚需+传承\"双重属性的理想选择。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "包含'市场洞察'关键词和专业分析结论",
        "alternatives": []
      },
      "processing_notes": "重要市场分析结论，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_029",
      "original_content": "#### 新房户型偏好\n- 三房户型：3068套（占比67%）\n- 四房户型：824套（占比18%）",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.8,
        "style_reasoning": "新房户型数据列表，包含分析属性",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_030",
      "original_content": "**市场洞察**：三房户型占据绝对主流，反映改善型需求主导新房市场，适合多代同堂的家庭资产规划。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "包含'市场洞察'关键词和专业分析结论",
        "alternatives": []
      },
      "processing_notes": "重要市场分析结论，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_031",
      "original_content": "## 3. 置业建议",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "SECTION",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown二级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown二级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_032",
      "original_content": "基于当前继承税政策及市场特征，我们提出以下策略建议：",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.8,
        "style_reasoning": "策略建议引言，包含'策略建议'关键词",
        "alternatives": [
          {
            "type": "TEXT",
            "style": "PLAIN",
            "type_confidence": 0.7,
            "style_confidence": 0.6,
            "reasoning": "普通引言文本"
          }
        ]
      },
      "processing_notes": "策略建议引言，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_033",
      "original_content": "**中老年置业者（50-65岁）**应重点考虑内环内80-100㎡的二居室，如静安寺、徐家汇等成熟板块。这类资产兼具养老自住和传承价值，且流动性好。数据显示70-90㎡户型成交占比达25%，市场接受度高。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "特定群体置业建议，包含数据支持和专业分析",
        "alternatives": []
      },
      "processing_notes": "重要策略建议，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_034",
      "original_content": "**高净值家庭**建议配置核心区域高品质大户型或别墅产品，如浦东前滩、黄浦滨江等板块。虽然200㎡以上房源仅占1.4%，但稀缺性保障长期价值。可充分利用继承免税优势实现家族资产跨代保值。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "特定群体置业建议，包含数据支持和专业分析",
        "alternatives": []
      },
      "processing_notes": "重要策略建议，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_035",
      "original_content": "**年轻购房者（30-40岁）**宜选中环地铁沿线优质学区房，如闵行春申、浦东联洋等板块。数据显示徐汇、静安学区房价格坚挺，4-6万元/㎡单价段成交占比近30%，既有教育功能又具资产传承价值。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "特定群体置业建议，包含数据支持和专业分析",
        "alternatives": []
      },
      "processing_notes": "重要策略建议，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_036",
      "original_content": "**多套房家庭**建议通过合理安排所有权登记，将不同房产分散在家庭成员名下。继承1000万房产仅需5000元印花税，相比买卖节省约30万税费，是优化家庭资产结构的有效方式。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "特定群体置业建议，包含数据支持和专业分析",
        "alternatives": []
      },
      "processing_notes": "重要策略建议，推荐BOARD样式突出显示"
    },
    {
      "segment_id": "seg_037",
      "original_content": "## 4. 风险提示与注意事项",
      "content_type": "title",
      "recommended_widget": {
        "primary_type": "TITLE",
        "primary_style": "SECTION",
        "type_confidence": 1,
        "style_confidence": 1,
        "style_reasoning": "标准markdown二级标题语法",
        "alternatives": []
      },
      "processing_notes": "强制规则：markdown二级标题必须推荐为TITLE控件"
    },
    {
      "segment_id": "seg_038",
      "original_content": "- **继承后转让税负**：再次出售需按差额20%缴个税，务必保留原购房凭证\n- **特殊房产限制**：拆迁安置房、共有产权房继承有特殊规定\n- **涉外继承**：涉及境外因素需专业法律支持\n- **未成年人继承**：处置流程复杂需提前规划\n- **债务风险**：继承房产同时继承相关房贷等债务",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "风险要点列表，包含专业法律和税务内容",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.9,
            "style_confidence": 0.8,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_039",
      "original_content": "专业建议：对于复杂继承情况，建议提前咨询专业律师和税务师，做好全生命周期资产规划。",
      "content_type": "paragraph",
      "recommended_widget": {
        "primary_type": "TEXT",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "专业建议结论，包含'专业建议'关键词",
        "alternatives": []
      },
      "processing_notes": "重要专业建议，推荐BOARD样式突出显示"
    }
  ],
  "analysis_metadata": {
    "step": 1,
    "total_segments": 39,
    "widget_recommendations": {
      "TITLE": 16,
      "TEXT": 12,
      "LIST": 9,
      "TABLE": 1,
      "CHART": 0
    },
    "style_recommendations": {
      "BOARD_style_count": 24,
      "high_confidence_recommendations": 35,
      "alternative_recommendations": 4
    },
    "high_priority_segments": [
      "seg_003",
      "seg_006",
      "seg_008",
      "seg_013",
      "seg_016",
      "seg_020",
      "seg_023",
      "seg_025",
      "seg_028",
      "seg_030",
      "seg_033",
      "seg_034",
      "seg_035",
      "seg_036",
      "seg_038",
      "seg_039"
    ],
    "processing_notes": "内容分析完成，严格遵循结构优先原则和强制标题规则，所有原始内容完整保存"
  }
}
```