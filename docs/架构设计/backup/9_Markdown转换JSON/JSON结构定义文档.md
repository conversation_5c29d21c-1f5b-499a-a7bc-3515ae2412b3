
## JSON控件结构定义

### 基础控件结构

```json
{
  "serial": "序列编号",
  "type": "控件类型",
  "style": "样式类型",
  "title": "控件标题(可选,移除加粗标记)"
}
```

### TITLE控件

```json
{
  "serial": "序列编号",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "标题内容"
}
```

**样式说明**：

- **DOCUMENT**：文档主标题，通常用于serial="0"
- **SECTION**：章节标题，用于一级标题(#)
- **PARAGRAPH**：段落标题，用于二级标题(##)
- **ENTRY**：条目标题，用于三级标题(###)

### TEXT控件

```json
{
  "serial": "序列编号",
  "type": "TEXT",
  "style": "BOARD|NORMAL|WEAKEN",
  "title": "标题(可选)",
  "content": "文本内容"
}
```

**样式说明**：

- **BOARD**：重要文本内容，带边框显示
- **NORMAL**：普通文本内容
- **WEAKEN**：弱化文本内容，用于次要信息或补充说明的呈现

### LIST控件

```json
{
  "serial": "序列编号",
  "type": "LIST",
  "style": "BOARD|SUDOKU|BULLET|NUMBER",
  "title": "列表标题(可选)",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容",
      "emphasize": true|false
    }
  ]
}
```

**样式说明**：

- **BOARD**：重点强调，带边框显示
- **SUDOKU**：以九宫格方式呈现的项目
- **BULLET**：普通项目符号列表
- **NUMBER**：编号列表

**字段说明**：

- **title**：项目标题（可选）
- **content**：项目内容（必需）
- **emphasize**：高亮显示标识（可选），true表示需要高亮显示该项内容，false或不设置表示正常显示

### TABLE控件

```json
{
  "serial": "序列编号",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "表格标题(可选)",
  "cols": [
    "列标题1",
    "列标题2"
  ],
  "content": [
    [
      {
        "type": "TEXT",
        "content": "单元格内容1"
      },
      {
        "type": "TEXT",
        "content": "单元格内容2",
        "recommended": true
      }
    ]
  ]
}
```

#### TableCell recommended属性应用规则

**适用场景**：对比性质表格中具有明显优势的数据项
- **使用标准**：价格优势、性能优势、配套优势、交通优势、数值最高/最低等明显优势
- **应用原则**：仅在真正具有明显优势的数据项上使用，推荐项不超过总数据项的30%
- **判断依据**：基于原始文档中的明确表述或数据对比结果
- **数值对比规则**：在数值对比表格中，最高价格、最大面积、最优性能等应标记为推荐

**房源对比中的应用场景**：
- **价格优势**：总价更低、单价更优、性价比更高的房源
- **面积优势**：建筑面积更大、使用面积更优的房源
- **配套优势**：地铁更近、学校更好、商业更便利的房源
- **品质优势**：装修更好、楼层更优、朝向更佳的房源
- **投资优势**：增值潜力更大、租售比更优的房源

**使用示例**：
```json
// 房源基础信息对比示例
[
  {"type": "TEXT", "content": "总价"},
  {"type": "TEXT", "content": "600万", "recommended": true},  // 价格更优
  {"type": "TEXT", "content": "800万"}  // 对比项
]

// 板块能级对比示例
[
  {"type": "TEXT", "content": "地铁距离"},
  {"type": "TEXT", "content": "245m", "recommended": true},  // 距离更近
  {"type": "TEXT", "content": "550m"}  // 对比项
]

// 小区品质对比示例
[
  {"type": "TEXT", "content": "容积率"},
  {"type": "TEXT", "content": "2.1"},
  {"type": "TEXT", "content": "1.8", "recommended": true}  // 容积率更低更优
]
```

**重要注意事项**：
- **第1列（对比维度）**：不设置recommended属性
- **第2列（房源A）**：根据分析结果设置true/false
- **第3列（房源B）**：根据分析结果设置true/false
- **推荐标准**：必须基于客观的数据对比和明确的优势判断

### CHART控件

```json
{
  "serial": "序列编号",
  "type": "CHART",
  "style": "PIE|BAR|LINE|MIXED",
  "title": "图表标题",
  "cols": [
    "X轴标签1",
    "X轴标签2"
  ],
  // PIE图不需要此字段
  "content": [
    {
      "title": "数据系列名称",
      "content": [
        数值1,
        数值2
      ],
      "chartType": "BAR|LINE" // 仅在style="MIXED"时需要指定
    }
  ]
}
```

**样式说明**：

- **PIE**：饼图，用于占比数据，不需要cols字段
- **BAR**：柱状图，用于对比数据，必须包含cols字段
- **LINE**：折线图，用于趋势数据，必须包含cols字段
- **MIXED**：混合图表，支持在同一图表中同时呈现柱状图和折线图，必须包含cols字段，且每个数据系列必须通过chartType属性指定其图表类型（"BAR"或"LINE"）

**日期格式说明**：
- X轴标签如果表示年月，必须使用"yyyy/MM"格式（例如："2024/01"）

### IMAGE控件

```json
{
  "serial": "序列编号",
  "type": "IMAGE",
  "style": "SINGLE|ALBUM",
  "title": "图片标题"
}
```

#### 单张图片模式（SINGLE）

```json
{
  "serial": "序列编号",
  "type": "IMAGE",
  "style": "SINGLE",
  "title": "图片标题",
  "content": {
    "url": "图片URL地址",
    "description": "图片描述(可选)"
  }
}
```

#### 相册模式（ALBUM）

```json
{
  "serial": "序列编号",
  "type": "IMAGE",
  "style": "ALBUM",
  "title": "相册标题",
  "content": {
    "defaultGroup": "默认展示的图片组名称(可选)",
    "groups": [
      {
        "title": "图片组标题",
        "images": [
          {
            "url": "图片URL地址",
            "description": "图片描述(可选)"
          }
        ]
      }
    ]
  }
}
```

**样式说明**：

- **SINGLE**：单张图片模式，直接展示一张图片
- **ALBUM**：相册模式，包含多个图片组，每个图片组具有标题和多张图片列表

**字段说明**：

- **url**：图片URL地址（必需）
- **description**：图片描述信息（可选）
- **defaultGroup**：默认展示的图片组名称（可选），用于指定相册模式下默认显示的图片组
- **groups**：图片组数组（相册模式必需）
- **title**：图片组标题（必需）
- **images**：图片列表（必需）
#### 基础结构
```json
{
  "serial": "3.1",
  "type": "CARD",
  "style": "BROKER|HOUSING|COMMUNITY",
  "title": "卡片标题",
  "fields": {
    // 根据样式类型确定具体字段
  }
}
```

#### BROKER卡片（经纪人）
```json
{
  "style": "BROKER",
  "fields": {
    "name": "姓名",
    "icon": "头像URL",
    "education": "学历",
    "experience": "服务年限",
    "serviceCategory": [
      "服务类别1",
      "服务类别2"
    ],
    "specialSkill": [
      "特色专长1",
      "特色专长1"
    ],
    "suggest": "投资建议",
    "wechat": "微信图片url",
    "phone": "联系电话"
  }
}
```

#### HOUSING卡片（房源）
```json
{
  "style": "HOUSING",
  "fields": {
    "layout": "户型",
    "area": "建筑面积",
    "floor": "楼层信息",
    "orientation": "朝向",
    "decoration": "装修状况",
    "totalPrice": "总价",
    "unitPrice": "单价",
    "propertyType": "房产类型"
  }
}
```

#### COMMUNITY卡片（小区）
```json
{
  "style": "COMMUNITY",
  "fields": {
    "buildYear": "建筑年代",
    "propertyCompany": "物业公司",
    "propertyFee": "物业费",
    "greenRate": "绿化率",
    "plotRatio": "容积率",
    "parkingSpaces": "停车位信息",
    "facilities": "主要配套设施"
  }
}
```