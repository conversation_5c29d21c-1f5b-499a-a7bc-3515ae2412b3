# 价值评测专家接口协议

## 1. 请求参数

```json
{
  "type": "HOUSING/COMMUNITY/BLOCK",
  "objectA": {},
  "objectB": {}
}
```

| 字段      | 类型     | 必填 | 说明                                             |
|---------|--------|----|------------------------------------------------|
| type    | string | 是  | `HOUSING` 房源对比;`COMMUNITY` 小区对比 ;`REGION` 地区对比 |
| objectA | object | 是  | 对比对象A,根据对比类型不同,分别是房源,小区或板块;                    |
| objectB | object | 是  | 对比对象B,根据对比类型不同,分别是房源,小区或板块;                    |

## 1.1 房源信息

### 字段

| 字段名                       | 字段描述   | 说明                                                                                | 是否必须     | 类型及长度              | 示例                                     |
|---------------------------|--------|-----------------------------------------------------------------------------------|----------|--------------------|----------------------------------------|
|                           |        | -----**_挂牌信息_** -----                                                             |          |                    |                                        |
| title                     | 标题     |                                                                                   | true     | string(100)        | 牛成村精装带阳台一房一可以宠物停车方便                    |
| description               | 描述     |                                                                                   | true     | string(300)        | 牛成村第二工业区房源，拎包即住，温馨如家                   |
| priceTotal                | 总价或租金  | - 新房&二手:万元<br>- 租房:元                                                              | true     | number(10,2)       | 1900                                   |
| priceUnit                 | 单价或押金  | 单位:元                                                                              | true     | number(10,2)       | 50000                                  |
| medias                    | 多媒体资料  | 媒体信息                                                                              | true     | array(object)      |                                        |
| &nbsp;\|- type            | 类型     | - IMAGE:图片<br>- VIDEO:视频<br>- AUDIO:音频                                            | true     | string(63)         | IMAGE                                  |
| &nbsp;\|- subtype         | 子类型    | - 实景图<br>- 户型图<br>- 视频<br>- 小区图                                                   | 租房必填     | string(63)         | 实景图                                    |
| &nbsp;\|- url             | 地址     |                                                                                   | true     | string(63)         | https://fyoss.fangyou.com/7d8ee08.jpg  |
| &nbsp;\|- cover           | 封面     | 视频时需要                                                                             | false    | string(63)         | https://fyoss.fangyou.com/97d8ee08.jpg |
| &nbsp;\|- name            | 名称     |                                                                                   | false    | string(63)         | 客厅                                     |
| labels                    | 关键字    | 可选值                                                                               | false    | array(string)      | \["拎包入住","拎包即住","南北通透"\]               |
| brokerService             | 中介服务   | - 中介费特惠<br>- 接送服务<br>- 十年房产经验<br>- 到店送礼<br>- 成交返红包好礼                              | false    | array(string)      | \["中介费特惠","接送服务"\]                     |
| listDate                  | 挂牌日期   |                                                                                   | 新房&二手房必填 | string(yyyy-MM-dd) | 2025-04-23                             |                                        |
| lookType                  | 看房时间类型 | - 随时可看<br>- 暂不可看<br>- 提前预约<br>- 周末可看                                              | false    | string(63)         | 随时可看                                   |
| checkinDate               | 入住日期   | 空表示随时入住                                                                           | false    | string(yyyy-MM-dd) | 2025-04-23                             |
|                           |        | -----**_小区信息_** -----                                                             |          |                    |                                        |
| communityAddress          | 小区地址   | 省市区名称即可                                                                           | true     | string(255)        | 广东省深圳市南山区                              |
| communityName             | 小区名    |                                                                                   | true     | string(127)        | 水湘人家                                   |
| propertyType              | 物业类型   | - 住宅 - 商住<br>- 别墅 - 商铺<br>- 写字楼<br>- 酒店式公寓<br>- 厂房 - 仓库<br>- 车位                   | 新房&二手房必填 | string(63)         | 住宅                                     |
| propertyManagementCompany | 物业管理公司 |                                                                                   | false    | string(127)        | 郑州凯旋物业管理有限公司                           |
| propertyManagementFee     | 物业管理费用 | 单位:元/㎡·月                                                                          | false    | number(10,2)       | 2.8                                    |
| parkingRatio              | 车位配比   |                                                                                   | false    | string(63)         | 1:1.2                                  |
| around                    | 周边配套   | - 餐饮 - 公交<br>- 学校 - 公园广场<br>- 超市 - 健身会馆<br>- 地铁房 - 菜场<br>- 医院 - 购物<br>- 便利店 - 可短租 | false    | array              | \["公交","学校","医院","购物","餐饮"\]           |
|                           |        | -----**_建筑信息_** -----                                                             |          |                    |                                        |
| houseStructure            | 房屋结构   | - 独栋 - 联排<br>- 复式 - 平层<br>- 跃层 - 错层<br>- loft - 其他                                | false    | string(63)         | 平层                                     |
| buildingCategory          | 建筑类别   | - 其他 - 低层<br>- 多层 - 小高层<br>- 高层 - 超高层                                             | false    | string(63)         | 多层                                     |
| buildingType              | 建筑类型   | - 板楼 - 塔楼<br>- 塔板结合 - 叠拼<br>- 独栋 - 联排<br>- 双拼 - 平房<br>- 其他                        | false    | string(63)         | 板楼                                     |
| completionTime            | 建成年代   | - 1937~2025                                                                       | 新房&二手房必填 | string(63)         | 2020                                   |
| totalFloor                | 总楼层    |                                                                                   | true     | integer(10)        | 10                                     |
| currentFloor              | 当前楼层   |                                                                                   | true     | integer(10)        | 5                                      |
| roomPerFloor              | 每层户数   |                                                                                   | false    | integer(10)        | 5                                      |
| tagElevator               | 是否有电梯  |                                                                                   | 新房&二手房必填 | boolean            | true                                   |
| elevatorCount             | 电梯数量   |                                                                                   | false    | integer(10)        | 3                                      |
|                           |        | -----**_房屋信息_** -----                                                             |          |                    |                                        |
| buildName                 | 楼栋名    |                                                                                   | 租房必填     | string(63)         | 1号楼                                    |
| unitName                  | 单元名    |                                                                                   | 租房必填     | string(63)         | 5单元                                    |
| roomNum                   | 房号     |                                                                                   | 租房必填     | string(63)         | 201                                    |
| buildingArea              | 建筑面积   |                                                                                   | true     | number(10,2)       | 100                                    |
| useArea                   | 使用面积   |                                                                                   | 合租必填     | number(10,2)       | 80                                     |
| efficiencyRate            | 得房率    |                                                                                   | false    | number(10,2)       | 0.8                                    |
| orient                    | 朝向     | - 朝东 - 朝西<br> - 朝南 - 朝北<br> - 东南 - 东北<br>- 西南 - 西北<br> - 南北 - 东西                  | 新房&二手房必填 | string(63)         | 朝东                                     | string(63)         | 朝南                                     |
| roomCount                 | 房间数    |                                                                                   | true     | integer(10)        | 3                                      |
| hallCount                 | 客厅数    |                                                                                   | true     | integer(10)        | 2                                      |
| toiletCount               | 卫生间数   |                                                                                   | true     | integer(10)        | 2                                      |
| redo                      | 装修情况编码 | - 毛坯 - 普装<br>- 精装 - 豪华                                                            | true     | string(63)         | 精装                                     |
| parking                   | 是否有车位  |                                                                                   | false    | boolean            | true                                   |
| saleReason                | 出售原因   |                                                                                   | false    | string(63)         |                                        |
| houseSituation            | 房屋现状   | - 空房 - 自住<br>- 租客住 - 其他                                                           | false    | string(63)         | 空房                                     |
| houseYears                | 房屋年限   | - 满五 - 满二<br>- 不满二                                                                | false    | string(63)         | 满五                                     |
| soleHouse                 | 唯一住房   |                                                                                   | false    | boolean            | true                                   |
|                           |        | -----**_权证信息_** -----                                                             |          |                    |                                        |
| housePlanPurpose          | 房屋规划用途 | - 住宅 - 公寓<br>- 酒店 - 综合<br>- 其他                                                    | false    | string(63)         | 住宅                                     |
| houseType                 | 产权类型   | - 商品房<br> - 经济适用房<br>- 回迁房<br> - 共有产权房<br>- 其他<br> - 公房                           | 新房&二手房必填 | string(63)         | 商品房                                    |

### 房源属性可选值

| 属性     | 场景  | 标签                                                                                                 |
|--------|-----|----------------------------------------------------------------------------------------------------|
| labels | 二手房 | - 满五 - 满二 - 不满二 - 近地铁 - 随时可看 - 新上 - 降价好房 - 满五唯一 - 唯一 - 七日热门 - 明厨明卫 - 带车位 - 南北通透 - 急售               |
| labels | 租房  | - 拎包即住 - 南北通透 - 家电齐全 - 交通便利 - 高速网络 - 允许宠物 - 精装修 - 带车位 - 近地铁 - 租金优惠 - 安静舒适 - 灵活租期 - 可短租 - 急租 - 拎包入住 |
| labels | 新房  | - 绿化率高 - 交通便利 - 车位充足 - 人车分流 - 品牌房企 - 置换改善 - 养老舒适 - 低密居所 - 精装交付 - 配套成熟                              |

### 示例:

```json
{
  "brokerId": 9095985342271461320,
  "sourceId": "%s",
  "buildingArea": 222,
  "type": "RENT",
  "useArea": 222,
  "roomNum": "010A",
  "communityName": "讴象社区菊园别墅二区",
  "roomCount": 1,
  "unitName": "无单元",
  "orient": "朝东",
  "level": "BROKER",
  "medias": [
    {
      "subtype": "实景图",
      "type": "IMAGE",
      "url": "https://fyoss-test.fangyou.com/250423163ec6487e0a3477bfb5f3209f010587e8b9c1e29e.jpg"
    }
  ],
  "toiletCount": 0,
  "totalFloor": 8,
  "hallCount": 0,
  "subType": "RENT_FULL",
  "currentFloor": 1,
  "description": "52525252525",
  "redo": "精装",
  "title": "别墅测试啊 1室 3000.00",
  "around": [
    "餐饮",
    "公交",
    "超市",
    "菜场"
  ],
  "communityAddress": "上海市嘉定区",
  "propertyType": "别墅",
  "priceTotal": 3000,
  "channelUp": true,
  "channelCode": "XIANYU",
  "buildName": "1幢",
  "equipments": [
    "衣柜",
    "热水器",
    "燃气",
    "冰箱",
    "空调"
  ],
  "payMonths": 2,
  "depositMonths": 1,
  "channelCodes": [
    "XIANYU",
    "ALIPAY"
  ]
}
```

## 1.2 小区信息

### 字段

| 字段                        | 名称         | 类型              | 示例                   | 说明                                                               |
|---------------------------|------------|-----------------|----------------------|------------------------------------------------------------------|
| id                        | 小区id       | number(20)      | 123456789            | 实现小区归一                                                           |
| name                      | 小区名        | string          | 慧芝湖花园                |                                                                  |
| beikeId                   | 贝壳小区id     | number(20)      | 123456789            | 实现小区归一                                                           |
| beikeName                 | 贝壳小区名      | string          | 慧芝湖花园                |                                                                  |
| address                   | 小区地址       | string          | 平型关路1083弄            |                                                                  |
| provinceName              | 省份名        | string          | 上海市                  |                                                                  |
| cityName                  | 城市名        | string          | 上海市                  |                                                                  |
| districtName              | 行政区名       | string          | 静安区                  |                                                                  |
| townName                  | 街道&镇名      | string          | 大宁路街道                |                                                                  |
| busiName                  | 商圈名        | string          | 凉城                   |                                                                  |
| formattedAddress          | 完整格式化地址    | string          | 上海市静安区大宁路街道慧芝湖花园     |                                                                  |
| typeName                  | 小区类型       | string          | 商务住宅;住宅区;住宅小区        |                                                                  |
| cityId                    | 城市id       | string          | 310100               |                                                                  |
| districtId                | 行政区id      | string          | 310106               |                                                                  |
| location                  | 坐标         | string          | 121.458773,31.281544 |                                                                  |
| latitude                  | 经度         | string          | 31.281544            |                                                                  |
| longitude                 | 纬度         | string          | 121.458773           |                                                                  |
| beikeIds                  | 贝壳小区id列表   | long[]          | \[1,2,3,4]           |                                                                  |
| detail                    | 小区详情       | json            |                      |                                                                  |
| - id                      | 详情主键       | number(20)      | 123456789            |                                                                  |
| - zipCd                   | 邮编         | string          | 200001               |                                                                  |
| - blockCd                 | 板块代码       | string          | 310106106            |                                                                  |
| - blockName               | 板块名称       | string          | 大宁                   |                                                                  |
| - proServiceAddr          | 物业服务中心地址   | string          | 紫园国际商务大厦4楼           | 福泰园一层,3号楼1单元负一楼,宝安区裕安路与107国道处,西溪山庄休闲中心13楼                        |
| - propertyOnTime          | 物业工作时间     | string          |                      |                                                                  |
| - intelGateFlag           | 是否安装智能道闸   | string          | true                 | true/false                                                       |
| - gateControlFlag         | 是否有门禁      | string          | true                 | true/false                                                       |
| - monitorFlag             | 是否有监控      | string          | true                 | true/false                                                       |
| - securityBoothNum        | 保安岗亭数      | string          |                      |                                                                  |
| - securityPersonNum       | 保安人数       | string          |                      |                                                                  |
| - securityAlldayFlag      | 是否24小时值守   | string          |                      |                                                                  |
| - securityPatrolFrequency | 保安巡察频率     | string          | 4.00                 |                                                                  |
| - policeNetworkingFlag    | 是否110联网    | string          |                      |                                                                  |
| - homeNameFlag            | 是否是总盘      | string          |                      |                                                                  |
| - propertyType            | 物业类型       | string          | 商业/商务公寓              | 商业/商务公寓,车库/普通住宅/工业厂房,普通住宅/商业/底商/商业办公类/商务公                        |
| - propertyYears           | 物业年限       | string          | 70,40/70             | 40/50,20/70,70/40,"70,40/70",40/50/70                            |
| - commBelong              | 交易权属       | string          | 商品房/使用权/拆迁安置房        | 商品房/使用权/拆迁安置房,商品房/已购公房/经济适用房/自建房                                 |
| - buildMaxYear            | 最大建筑年代     | string          | 2009                 |                                                                  |
| - buildMinYear            | 最小建筑年代     | string          | 2004                 |                                                                  |
| - buildNum                | 楼栋总数       | string          |                      |                                                                  |
| - houseNum                | 房屋总数       | string          |                      |                                                                  |
| - developerCorp           | 开发商        | string          | 中昊嘉信城市发展集团           |                                                                  |
| - brandCorp               | 品牌商        | string          | 成都城投置地（集团）有限公司       |                                                                  |
| - actArea                 | 占地面积       | string          |                      |                                                                  |
| - buildArea               | 建筑面积       | string          |                      |                                                                  |
| - buildingType            | 建筑类型       | string          | 板楼,塔板结合              | 合院别墅,塔楼、板楼、塔板结合、高层、超高层、小高层                                       |
| - houseType               | 房屋类型       | string          | 低层/小高层/高层            | 低层/多层/小高层/高层,低层/高层,小高层/高层/超高层,低层/多层,低层/多层/高层                     |
| - buildingCategory        | 建筑类别       | string          | 商住/独栋                | 商住/独栋,花园洋房/独栋,商住/叠拼,花园洋房/商住/独栋,花园洋房/叠拼                           |
| - propertyName            | 物业公司       | string          | 沈阳中环物业管理有限公司         | 沈阳中环物业管理有限公司,东莞市永泰物业有限公司		,京新物业管理有限公司                            |
| - propertyFee             | 物业费        | string          | 1.38-3元/月/㎡          | 1.38-3元/月/㎡,0.98-2.23元/月/㎡,3-5.11元/月/㎡                           |
| - propertyPhone           | 物业电话       | string          | 021-32300162         | 021-32300162,0571-88761000,020-85686293,010-66214377             |
| - communityCloseFlag      | 小区是否封闭     | string          | true                 | false,true                                                       |
| - parkingRate             | 车位配比率(户:车) | string          | 1:1.55               | 1:1.55,1:6,1:0.186,1.00:1.01,1:4.92,1:2.80,1:4.59,1.00:0.20      |
| - upParkingNum            | 地上车位数      | number          | 1025                 | 1025,3000,588,88,332,404                                         |
| - downParkingNum          | 地下车位数      | string          |                      |                                                                  |
| - parkingNum              | 车位总数       | string          |                      |                                                                  |
| - personDivCarFlag        | 是否人车分流     | string          | true                 | ture/false                                                       |
| - parkingSaleFlag         | 是否出售产权车位   | string          | true                 | ture/false                                                       |
| - setParkingFee           | 固定停车费标准    | string          | 400/位                | 400/位,地下：400-500/位,地上250 地下250,18,"地上:70/位,地下:250/位",第一辆30 第二辆50 |
| -  tempParkingFee         | 临停停车费标准    | string          | 10元/小时               | 10元/小时,3元/天                                                      |
| - volumeRate              | 容积率        | string          | 4.42                 | 4.42,2.39,5.35,0.86,8.30,2.54,4.62                               |
| - greenRate               | 绿化率        | string          | 0.70                 | 0.70,0.33,0.75,0.83,0.52,0.68                                    |
| - powerdDesc              | 供电描述       | string          | 民电，电费0.52元/度         | 一户一表，自行买电,民用电；用电量分三个档次、低于204度部分，,环网供电、月缴,3元/度                    |
| - waterDesc               | 供水描述       | string          | 居民用水，2.3元/立方米        | 小区供水,居民供水,商业,湖州自来水公司,高层住宅7层以上（含7层）无负压变频供水                        |
| -  gasDesc                | 供气描述       | string          | 2.38-3.3元/m³         | 3.03-3.5元/月/m³,2.48-2.65元/月/㎡,天然气入户,0.4-2.55元/月/m³               |
| -  heatingDesc            | 供暖描述       | string          | 集中供暖26.7元/平方         | 无集中供暖,节能、环保、健康式采暖系统,自购供暖                                         |
| -  iconUrl                | 小区图标       | string          |                      |                                                                  |
| layoutsSummaryMap         | 小区户型汇总     | Map<'户型名',json> |                      |                                                                  |
| - <户型名>                   | 户型名        | string          | 3室2厅                 |                                                                  |
| - - roomCount             | 卧室数        | number          | 3                    |                                                                  |
| - - hallCount             | 客厅数        | number          | 2                    |                                                                  |
| - - setNum                | 套数         | number          | 12                   |                                                                  |
| - - areaMax               | 最大面积       | number          | 88.56                |                                                                  |
| - - areaMin               | 最小面积       | number          | 70.29                |                                                                  |
| - - picUrl                | 户型图        | string[]        | url                  |                                                                  |
| picturesMap               | 小区图片       | json[]          |                      |                                                                  |
| - <图片类型>                  | 图片类型       | string          | 景观带                  | 出入口,远景,道路,停车场,楼栋,入户门,景观带,配套,分布图,其他                               |
| - - <图片列表>                | 图片类别       | string[]        | url                  |                                                                  |

### 示例

```json
{
  "id": 1,
  "name": "慧芝湖花园",
  "address": "平型关路1083弄",
  "beikeId": 5020069179760703,
  "beikeName": "慧芝湖花园（三期）",
  "busiName": "凉城",
  "cityCode": "310100",
  "cityId": 310100,
  "cityName": "上海市",
  "districtId": 310106,
  "districtName": "静安区",
  "formattedAddress": "上海市静安区大宁路街道慧芝湖花园",
  "latitude": 31.281544,
  "location": "121.458773,31.281544",
  "longitude": 121.458773,
  "provinceName": "上海市",
  "townName": "大宁路街道",
  "typeName": "商务住宅;住宅区;住宅小区",
  "detail": {
    "id": 129712,
    "blockCd": "310106106",
    "blockName": "大宁",
    "buildMaxYear": "2009",
    "buildMinYear": "2004",
    "buildNum": 9,
    "buildingType": "板楼",
    "commBelong": "商品房/使用权",
    "createTime": "2024-12-31T15:00:49",
    "developerCorp": "嘉华(中国)投资有限公司",
    "downParkingNum": 0,
    "gasDesc": "3元/m³",
    "greenRate": 0.45,
    "heatingDesc": "自采暖",
    "houseNum": 3526,
    "parkingNum": 3494,
    "parkingRate": "1.00:0.70",
    "powerdDesc": "民电",
    "propertyFee": "2.7元/月/㎡",
    "propertyName": "龙湖物业",
    "propertyPhone": "021-66525123",
    "propertyType": "住宅",
    "propertyYears": "50/70",
    "setParkingFee": "300",
    "upParkingNum": 0,
    "updateTime": "2025-01-09T15:11:45",
    "version": 2,
    "volumeRate": 2.50,
    "waterDesc": "民水"
  },
  "layoutsSummaryMap": {
    "1室2厅": {
      "areaMax": 88.56,
      "areaMin": 70.29,
      "hallCount": 2,
      "name": "1室2厅",
      "picUrl": [
        "https://oss-consumer.ebaas.com/community_layout/129712/5f9ce615-2fd3-4d01-b95a-1be41ddcb495.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/d697791a-32be-461f-97bf-807266a1b262.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/ab966172-90cb-4116-b5f5-7af48e78fdc1.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/09513af2-c135-45ea-b8d4-144c78d94c9e.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/cf69a04e-9234-4675-8434-87f8dde4fa5f.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/e2dfc091-472f-464c-84be-3075377c4a33.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/37779854-64b7-42e6-bcd7-458656857ecf.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/d2276afc-e980-468c-bdd8-e3af192b18dd.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/a33c37f1-25c1-4e45-bdce-790ca28dd702.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/22630147-9fef-4447-87a8-411177125b75.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/55743f0d-c688-4400-8797-32363b386391.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/5eb5260e-c2a5-4808-b7d0-13695a0e774e.jpg"
      ],
      "roomCount": 1,
      "setNum": 12
    },
    "1室1厅": {
      "areaMax": 79.00,
      "areaMin": 72.44,
      "balconyCount": 2,
      "hallCount": 1,
      "kitchenCount": 1,
      "name": "1室1厅",
      "picUrl": [
        "https://oss-consumer.ebaas.com/community_layout/129712/c5abe500-df5f-43ae-9810-9a99cbf3931c.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/90f2a195-1269-48a8-bdb7-3276dae69864.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/994500f1-63b5-495c-9cdd-acca6fb8eff4.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/2bedf1cb-7343-4a68-bb71-4f66c0a5fb72.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/87e03e8a-9a87-41f4-b848-82626865b3ba.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/16021c5f-9c3b-4487-a118-be073e4fc6ec.jpg"
      ],
      "roomCount": 1,
      "setNum": 6,
      "toiletCount": 1
    },
    "2室1厅": {
      "areaMax": 107.00,
      "areaMin": 73.04,
      "balconyCount": 1,
      "hallCount": 1,
      "kitchenCount": 1,
      "name": "2室1厅",
      "picUrl": [
        "https://oss-consumer.ebaas.com/community_layout/129712/d33c326b-dbbb-45b4-852c-43c97c6b26c0.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/c8845078-8090-47ee-8c6d-28c748e289c3.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/baec009d-03fa-47df-8b06-f3c00871c0c9.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/d022237d-525d-4bfe-8e8e-a989c5edb8f8.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/31900555-ea7f-4632-a826-be9c3b950e60.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/704b445c-54a3-4bdf-8a89-e26d968f2d6b.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/54a82bf9-c9b8-47c7-aa78-4f894631dac9.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/b6f77457-ba09-443a-97a0-7a4dc63292e0.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/86227575-e2a6-4a7b-b2b0-5edb6f09aac1.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/54bc33a0-21f4-44fc-b8b3-7ccd77d9220f.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/a4e35d74-ea48-4c10-a61e-ddb897fac06c.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/93d825f5-b603-4f2d-9bb2-ad84a6a9b5c8.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/1688059e-43f9-4b36-bb4d-97ccb459b0f9.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/6188fcaa-165f-4252-af0d-2337b5e676cb.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/ba8df067-ecab-4879-95be-1454375f57c9.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/4aa5dc32-b24d-4307-8fe5-e59c5f251d05.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/c17813b5-4d95-4887-a0fe-53bf0169e52b.jpg"
      ],
      "roomCount": 2,
      "setNum": 34,
      "toiletCount": 1
    },
    "2室2厅": {
      "areaMax": 107.69,
      "areaMin": 72.44,
      "hallCount": 2,
      "name": "2室2厅",
      "picUrl": [
        "https://oss-consumer.ebaas.com/community_layout/129712/5c25520e-a9ba-4fea-ba50-f114b0045eec.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/f8a34698-460e-4081-be76-ac4f6358783f.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/d4cfc4ae-8577-4ded-b89e-d4395c19bdff.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/de847ef6-1f9e-45d9-a926-2993d69c231b.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/d46e126c-d2fc-4c77-98d7-78011227e0a8.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/1a83bf40-7ebb-4175-905f-e05ab13cc08d.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/a128c2d5-af73-42af-a4ed-962ea5a575dd.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/17a654c7-516a-45f7-ba77-b7f6d7635a59.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/68d77614-ca26-4246-b02b-afd4d3856ba6.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/5de50209-8735-4378-8a2f-1fdd4d1664af.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/116bc488-be3c-4869-bde9-6a3d92a818dc.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/7ea5d175-4464-49a2-95d8-d81c71490b4f.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/b90b8f74-2fcc-472c-95a1-cf1a3b2d3fd4.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/f6343115-21bc-4860-a9fb-2a56bb8e9b8b.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/9f4b5bf1-2897-4fc2-a0c0-1671664132ad.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/4c184efa-17c4-41c5-95b1-f850a5c84b24.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/224c4898-d821-4a39-a0b6-ec37f974bf57.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/c007286a-ee45-49b8-bec5-c55b067ff2f1.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/e428e6d2-0f55-46dd-b11a-f5d2992a8b12.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/5bb9938d-20d6-42dd-8626-535f98142a92.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/9686fde7-ec68-4967-b3ed-13035342c4db.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/fec5dc11-fbfb-48ae-bc7c-84c2739a5a4d.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/7e85149e-ae3e-43fa-ad7e-e66891b96feb.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/b37361d4-e2e2-45c0-abb7-53aaec0cea64.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/cdc71dbf-85f2-436f-8d24-aa2039c46c91.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/29a1b504-85f4-4816-8d96-e1c45e4ef62f.jpg"
      ],
      "roomCount": 2,
      "setNum": 52
    },
    "3室1厅": {
      "areaMax": 154.10,
      "areaMin": 80.98,
      "balconyCount": 2,
      "hallCount": 1,
      "kitchenCount": 1,
      "name": "3室1厅",
      "picUrl": [
        "https://oss-consumer.ebaas.com/community_layout/129712/575d21f1-a006-4a94-a7c6-580e44526012.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/a50a3d97-1d9a-4d75-b723-66b868508064.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/0399cf1d-a125-48c7-9786-09cc0b001644.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/78ca22c9-c7e0-458a-ad76-a26709cc5279.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/9ea813cb-cb29-489e-b706-9ea009769041.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/0cb94bb2-f993-42cc-835b-eaaf34a80b98.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/f4710f84-e12b-4f0e-a7dc-47964d08a5c2.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/d4dccfb1-4a01-4d40-b049-078cd495872e.jpg"
      ],
      "roomCount": 3,
      "setNum": 24,
      "toiletCount": 2
    },
    "3室2厅": {
      "areaMax": 109.77,
      "areaMin": 88.35,
      "balconyCount": 1,
      "hallCount": 2,
      "kitchenCount": 1,
      "name": "3室2厅",
      "picUrl": [
        "https://oss-consumer.ebaas.com/community_layout/129712/8fa57921-6ae9-40ec-b908-1354ec8c96a1.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/9be33f94-6fc7-4e25-938d-f48ceabede2f.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/35bd1042-5590-40e4-be80-121200c606fd.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/75b1a0bc-0002-42e2-8f74-4444388048e2.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/274d65d5-c83c-4c18-940b-3aa8274a889a.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/08041c30-6243-4a2a-a074-edc7a491bd83.jpg"
      ],
      "roomCount": 3,
      "setNum": 18,
      "toiletCount": 1
    },
    "4室1厅": {
      "areaMax": 193.24,
      "areaMin": 193.24,
      "balconyCount": 1,
      "hallCount": 1,
      "kitchenCount": 1,
      "name": "4室1厅",
      "picUrl": [
        "https://oss-consumer.ebaas.com/community_layout/129712/63882253-ae12-4104-bcbc-9ec2b2568366.jpg"
      ],
      "roomCount": 4,
      "setNum": 4,
      "toiletCount": 3
    },
    "4室2厅": {
      "areaMax": 173.82,
      "areaMin": 95.16,
      "hallCount": 2,
      "name": "4室2厅",
      "picUrl": [
        "https://oss-consumer.ebaas.com/community_layout/129712/69713ad8-471c-446b-82f0-d23a70dfb323.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/24d32dab-003c-4c45-873b-82c1753ff122.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/ef87c00b-dbb8-422f-ad49-dca8bc7b43eb.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/6c658490-a3c6-4998-9384-d5bb0e8f5192.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/4dd843f6-7dfe-4d75-8fd1-9c87461fe2dd.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/fddab51e-fdb1-42f8-9707-3bfab6830d8e.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/4ac8187e-a9fc-480a-998c-1f5fee6af129.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/c64aee10-36b7-441f-8fe9-9b658f6b411a.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/b07ed0cd-fc36-40df-ad78-143ccc8684a9.jpg",
        "https://oss-consumer.ebaas.com/community_layout/129712/106eef48-0888-4f07-b308-895e92ad2e86.jpg"
      ],
      "roomCount": 4,
      "setNum": 40
    },
    "4室3厅": {
      "areaMax": 184.21,
      "areaMin": 184.21,
      "balconyCount": 1,
      "hallCount": 3,
      "kitchenCount": 1,
      "name": "4室3厅",
      "picUrl": [
        "https://oss-consumer.ebaas.com/community_layout/129712/8e3ff767-b10e-441b-acd5-3cbd71781875.jpg"
      ],
      "roomCount": 4,
      "setNum": 4,
      "toiletCount": 3
    }
  },
  "picturesMap": {
    "景观带": [
      "https://oss-consumer.ebaas.com/community_picture/129712/9a6a9e4c-910a-4c59-a23d-c7139c65d977.jpg"
    ],
    "楼栋": [
      "https://oss-consumer.ebaas.com/community_picture/129712/8b1ebf63-1bd2-43cd-bef4-19be36028123.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/84d3ed2d-cd25-4234-b216-712b8a55d9e9.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/61b50115-7f66-407e-84cc-4fe411328ba4.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/41998c0d-0c8b-4166-b9ac-b88e5fabbec1.jpg"
    ],
    "远景": [
      "https://oss-consumer.ebaas.com/community_picture/129712/669a8c30-dd3e-47ec-8d84-8eb1270af886.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/6e8cdf8e-0f8d-4ff3-ac90-5e2b43cd6993.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/7568bea0-8562-46e7-918c-4030112f554b.jpg"
    ],
    "出入口": [
      "https://oss-consumer.ebaas.com/community_picture/129712/4e5ccd03-e53a-4947-8fa5-92377fb667de.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/bf36c1dc-1d96-4b92-8e0a-9557faa8256a.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/ffa6345b-9491-44d5-b89f-85030a77db01.jpg"
    ],
    "入户门": [
      "https://oss-consumer.ebaas.com/community_picture/129712/2b86547c-0a1e-4966-8420-b282d98c6c23.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/5002fc3d-6ac5-4159-a7ee-6462ab03ae0b.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/21dcdab4-096a-4017-bdda-e70909762b09.jpg"
    ],
    "分布图    ": [
      "https://oss-consumer.ebaas.com/community_picture/129712/83c455ea-11dc-47d1-8202-5221a4406aa1.jpg"
    ],
    "其他": [
      "https://oss-consumer.ebaas.com/community_picture/129712/9374425c-dde1-4af3-8dcb-7fbaa8036bed.jpg"
    ],
    "道路": [
      "https://oss-consumer.ebaas.com/community_picture/129712/e8a6de00-afa1-4ee0-b58c-a2e42f7f0b09.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/e2644c9f-e70a-4cd0-a07b-cba60cc878b6.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/737a59b1-2db1-4297-8f58-e9573233aeee.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/ff213483-ee1e-4e92-9c24-2c12820d4bb6.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/1fe2ba85-8cf6-4db8-8516-609d0c0014ff.jpg"
    ],
    "停车场": [
      "https://oss-consumer.ebaas.com/community_picture/129712/6ffd978c-36a0-47db-895a-dd56938174f7.jpg",
      "https://oss-consumer.ebaas.com/community_picture/129712/fcd4718b-9600-4ecd-9b7c-298e472fe9d7.jpg"
    ]
  }
}
```

### 1.3 地区信息

```json
{
  "type": "CITY/DISTRICT/BLOCK",
  "name": "上海/上海静安/上海市静安区大宁板块"
}
```

| 字段   | 类型     | 必填 | 说明                                         |
|------|--------|----|--------------------------------------------|
| type | string | 是  | 地区类型(`CITY` 城市;`DISTRICT` 行政区;`BLOCK` 板块;) |
| name | string | 是  | 上海/上海静安/上海市静安区大宁板块                         |

## 2. 应答数据样例

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "title": "同地段价差150万的小区如何选择?",
    "widgets": [
      {
        "serial": "1",
        "type": "TEXT",
        "style": "SECTION",
        "title": "房源基础信息"
      },
      {
        "serial": "1.1",
        "type": "TABLE",
        "columns": [
          "房源核心参数",
          "一品漫城(一期)",
          "浦江华侨城(三期)"
        ],
        "rows": [
          [
            {
              "type": "TEXT",
              "content": "总价"
            },
            {
              "type": "TEXT",
              "content": "600万(业主急售)"
            },
            {
              "type": "TEXT",
              "content": "465万(业主急售)",
              "recommended": true
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "单价测算"
            },
            {
              "type": "TEXT",
              "content": "600万/87.76㎡ ≈ 6.84万/㎡"
            },
            {
              "type": "TEXT",
              "content": "465万/78.95㎡ ≈ 5.90万/㎡",
              "recommended": true
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "参考预算"
            },
            {
              "type": "TEXT",
              "content": "首付200万，月供21640元"
            },
            {
              "type": "TEXT",
              "content": "首付186万，月供16771元",
              "recommended": true
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "房屋年代"
            },
            {
              "type": "TEXT",
              "content": "2013年",
              "recommended": true
            },
            {
              "type": "TEXT",
              "content": "2012年"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "物业类型"
            },
            {
              "type": "TEXT",
              "content": "70年产权普通住宅"
            },
            {
              "type": "TEXT",
              "content": "70年产权普通住宅"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "面积（平方米）"
            },
            {
              "type": "TEXT",
              "content": "87.76㎡",
              "recommended": true
            },
            {
              "type": "TEXT",
              "content": "78.95㎡"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "户型"
            },
            {
              "type": "TEXT",
              "content": "2室2厅1卫"
            },
            {
              "type": "TEXT",
              "content": "2室2厅1卫"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "户型图"
            },
            {
              "type": "IMAGE",
              "content": "https://oss-consumer.ebaas.com/community_layout/129712/69713ad8-471c-446b-82f0-d23a70dfb323.jpg"
            },
            {
              "type": "IMAGE",
              "content": "https://oss-consumer.ebaas.com/community_layout/129712/8fa57921-6ae9-40ec-b908-1354ec8c96a1.jpg"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "房源实拍"
            },
            {
              "type": "IMAGE",
              "content": "https://fyoss-test.fangyou.com/250423163ec6487e0a3477bfb5f3209f010587e8b9c1e29e.jpg"
            },
            {
              "type": "IMAGE",
              "content": "https://fyoss-test.fangyou.com/250423163ec6487e0a3477bfb5f3209f010587e8b9c1e29e.jpg"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "朝向"
            },
            {
              "type": "TEXT",
              "content": "南北通",
              "recommended": true
            },
            {
              "type": "TEXT",
              "content": "南"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "楼层"
            },
            {
              "type": "TEXT",
              "content": "11F/12F（中高区采光优）",
              "recommended": true
            },
            {
              "type": "TEXT",
              "content": "8F/12F（中区视野平稳）"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "车位"
            },
            {
              "type": "TEXT",
              "content": "两梯四户，带固定长租车位",
              "recommended": true
            },
            {
              "type": "TEXT",
              "content": "一梯六户，无车位"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "装修"
            },
            {
              "type": "TEXT",
              "content": "精装（维护9成新）",
              "recommended": true
            },
            {
              "type": "TEXT",
              "content": "精装（保养8成新）"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "房本年限"
            },
            {
              "type": "TEXT",
              "content": "满五年"
            },
            {
              "type": "TEXT",
              "content": "满五年"
            }
          ]
        ]
      },
      {
        "serial": "1.2",
        "type": "LIST",
        "style": "ITEM",
        "title": "对比结论",
        "rows": [
          {
            "title": "价差逻辑",
            "content": "华侨城单价低约 1.1 万 /㎡，主因小户型占比高拉低均价，同面积段实际差价约 8% 。"
          },
          {
            "title": "核心基础差异",
            "content": "一品漫城面积多 9㎡，主要面积多在主卧，层高更高通风采光潜力大。装修用材更优（大理石台面 + 实木地板）且带车位，便于有车用户出行 。"
          }
        ]
      },
      {
        "serial": "2",
        "type": "TEXT",
        "style": "SECTION",
        "title": "小区品质"
      },
      {
        "serial": "2.1",
        "type": "TABLE",
        "columns": [
          "小区核心参数",
          "一品漫城(一期)",
          "浦江华侨城(三期)"
        ],
        "rows": [
          [
            {
              "type": "TEXT",
              "content": "容积率"
            },
            {
              "type": "TEXT",
              "content": "0.92",
              "recommended": true
            },
            {
              "type": "TEXT",
              "content": "1.2"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "小区实景"
            },
            {
              "type": "IMAGE",
              "content": "https://oss-consumer.ebaas.com/community_picture/129712/669a8c30-dd3e-47ec-8d84-8eb1270af886.jpg"
            },
            {
              "type": "IMAGE",
              "content": "https://oss-consumer.ebaas.com/community_picture/129712/4e5ccd03-e53a-4947-8fa5-92377fb667de.jpg"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "外立面"
            },
            {
              "type": "TEXT",
              "content": "米黄色涂料，房源所属楼栋有渗水痕迹"
            },
            {
              "type": "TEXT",
              "content": "灰色石材外立面，无脱落老化",
              "recommended": true
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "绿化率"
            },
            {
              "type": "TEXT",
              "content": "0.35"
            },
            {
              "type": "TEXT",
              "content": "0.4",
              "recommended": true
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "小区配套"
            },
            {
              "type": "TEXT",
              "content": "人车半分流，儿童乐园与健身设施齐全"
            },
            {
              "type": "TEXT",
              "content": "人车全分流，纯花园社区，儿童乐园与健身设施齐全",
              "recommended": true
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "物业服务"
            },
            {
              "type": "TEXT",
              "content": "保洁与绿化维护中规中矩"
            },
            {
              "type": "TEXT",
              "content": "安保24小时巡逻 + 电梯厅每日消毒",
              "recommended": true
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "物业费"
            },
            {
              "type": "TEXT",
              "content": "上海春川物业，2.6元/㎡/月",
              "recommended": true
            },
            {
              "type": "TEXT",
              "content": "华侨城自持物业，3元/㎡/月"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "车位配比"
            },
            {
              "type": "TEXT",
              "content": "0.41:1，带车位房源稀缺",
              "recommended": true
            },
            {
              "type": "TEXT",
              "content": "1:1.5，车位基本全满"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "停车费"
            },
            {
              "type": "TEXT",
              "content": "车位市价约25万/个，月租300元/月",
              "recommended": true
            },
            {
              "type": "TEXT",
              "content": "车位市价约25万/个，月租450元/月"
            }
          ]
        ]
      },
      {
        "serial": "2.2",
        "type": "TEXT",
        "style": "BLOCK",
        "title": "实勘建议",
        "content": "华侨城在社区质感与物业服务水平上优势显著，适合有孩家庭；一品漫城需重点检查屋顶防水。一品漫城车位紧张，但本房源业主持有长租固定车位，在同小区房源内较为稀缺。"
      },
      {
        "serial": "3",
        "type": "TEXT",
        "style": "SECTION",
        "title": "板块能级"
      },
      {
        "serial": "3.1",
        "type": "TABLE",
        "columns": [
          "板块核心参数",
          "一品漫城(一期)",
          "浦江华侨城(三期)"
        ],
        "rows": [
          [
            {
              "type": "TEXT",
              "content": "所属板块"
            },
            {
              "type": "TEXT",
              "content": "浦锦街道"
            },
            {
              "type": "TEXT",
              "content": "浦江镇"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "地铁"
            },
            {
              "type": "TEXT",
              "content": "8号线芦恒路站(245m),早晚高峰拥挤"
            },
            {
              "type": "TEXT",
              "content": "8号线浦江镇站(550m),拥挤程度尚可"
            }
          ],[
            {
              "type": "TEXT",
              "content": "拥堵情况"
            },{
              "type": "TEXT",
              "content": "早高峰浦星路拥堵严重，去前滩5公里常拥堵30分钟"
            },{
              "type": "TEXT",
              "content": "同样拥堵，本小区更近距离济阳路高架入口"
            }
          ],[
            {
              "type": "TEXT",
              "content": "交通利好"
            },{
              "type": "TEXT",
              "content": "银都路隧道已建成（10分钟至浦西），机场联络线三林南站（在建）"
            },{
              "type": "TEXT",
              "content": "在建19号线（浦锦路站）将分流，步行可达，可提升小区交通便利，房价上升潜力大"
            }
          ],[
            {
              "type": "TEXT",
              "content": "大型商场"
            },{
              "type": "TEXT",
              "content": "东方懿德城（1.3km）"
            },{
              "type": "TEXT",
              "content": "O'MALL华侨城中心（300m）"
            }
          ],[
            {
              "type": "TEXT",
              "content": "超市"
            },{
              "type": "TEXT",
              "content": "联华超市（285m）"
            },{
              "type": "TEXT",
              "content": "家乐福（1.3km）"
            }
          ],[
            {
              "type": "TEXT",
              "content": "学校"
            },{
              "type": "TEXT",
              "content": "上海实验小学（公立816m），有消息称，对口学校将改为上师大附属，小区未来溢价潜力大"
            },{
              "type": "TEXT",
              "content": "上师大三附小（公立1.4km）"
            }
          ],[
            {
              "type": "TEXT",
              "content": "医院"
            },{
              "type": "TEXT",
              "content": "华山南院（3.7km，三甲）"
            },{
              "type": "TEXT",
              "content": "仁济医院（1.5km，三甲）"
            }
          ]
        ]
      },
      {
        "serial": "3.2",
        "type": "TEXT",
        "style": "BLOCK",
        "title": "实勘建议",
        "content": "一品漫城距地铁站更近，但站点拥堵程度高。华侨城距医院更近，适合有老人和孩子的家庭。两个小区距离相近，出行难易度基本一致。华侨城更靠近前滩中央活动区（CAZ），可享受高端产业人口外溢红利（前滩 1000 万仅买 80㎡，华侨城同总价可得改善空间） 。"
      },
      {
        "serial": "4",
        "type": "TEXT",
        "style": "SECTION",
        "title": "市场成交"
      },
      {
        "serial": "4.1",
        "type": "LINE_CHART",
        "title": "近一年所属板块成交趋势",
        "x_axis": [
          "2025年1月",
          "2025年2月",
          "2025年3月",
          "2025年4月",
          "2025年5月",
          "2025年6月"
        ],
        "series": [
          {
            "name": "本楼盘",
            "data": [
              6.8,
              6.6,
              6.4,
              6.2,
              6.0,
              5.8
            ]
          },
          {
            "name": "同板块新房",
            "data": [
              6.6,
              6.4,
              6.2,
              6.0,
              5.8,
              5.6
            ]
          }
        ]
      },
      {
        "serial": "4.2",
        "type": "TABLE",
        "title": "近期小区成交数据对比",
        "columns": [
          "参数类别",
          "一品漫城(87.76m²户型)",
          "浦江华侨城(78-80m²户型)"
        ],
        "rows": [
          [
            {
              "type": "TEXT",
              "content": "成交均价"
            },
            {
              "type": "TEXT",
              "content": "65130元/m²"
            },
            {
              "type": "TEXT",
              "content": "67917元/m²"
            }
          ],
          [
            {
              "type": "TEXT",
              "content": "成交总价"
            },
            {
              "type": "TEXT",
              "content": "571~1580万元"
            },
            {
              "type": "TEXT",
              "content": "615~845万元"
            }
          ],[
            {
              "type": "TEXT",
              "content": "挂牌均价"
            },{
              "type": "TEXT",
              "content": "71513元/㎡"
            },{
              "type": "TEXT",
              "content": "67917元/㎡"
            }
          ],[
            {
              "type": "TEXT",
              "content": "挂牌总价"
            },{
              "type": "TEXT",
              "content": "440 - 2300万元"
            },{
              "type": "TEXT",
              "content": "390 - 1000万元"
            }
          ],[
            {
              "type": "TEXT",
              "content": "目前在售"
            },{
              "type": "TEXT",
              "content": "23套"
            },{
              "type": "TEXT",
              "content": "25套"
            }
          ],[
            {
              "type": "TEXT",
              "content": "在售降价"
            },{
              "type": "TEXT",
              "content": "8套"
            },{
              "type": "TEXT",
              "content": "15套"
            }
          ],[
            {
              "type": "TEXT",
              "content": "在售涨价"
            },{
              "type": "TEXT",
              "content": "1套"
            },{
              "type": "TEXT",
              "content": "0套"
            }
          ],[
            {
              "type": "TEXT",
              "content": "近6月成交"
            },{
              "type": "TEXT",
              "content": "6套，近6月成交均价6.4 - 6.8万/㎡，面积80 - 100㎡"
            },{
              "type": "TEXT",
              "content": "4套，近6月成交均价6.6 - 6.8万/㎡，面积90 - 95㎡"
            }
          ]
        ]
      },
      {
        "serial": "4.3",
        "type": "TEXT",
        "style": "BLOCK",
        "title": "置业专家建议",
        "content": "当下是购房窗口期,两套房源均因业主资金周转急售。一品漫城2025年5月同面积挂牌价普遍在620-650万,此套房源低于市场价3%-5%,且带固定车位。华侨城降价空间更大(可尝试430-450万切入),2024年同类房源成交价约480万(单价6.1万/m²),当前465万急售,低于行情3%。但要注意小户型流通快(年均去化20套),需尽快下手。"
      },
      {
        "serial": "5",
        "type": "TEXT",
        "style": "SECTION",
        "title": "购买策略"
      },
      {
        "serial": "5.1",
        "type": "TABLE",
        "columns": [
          "一品漫城",
          "浦江华侨城"
        ],
        "rows": [
          [
            {
              "type": "TEXT",
              "content": "预算充裕且必须车位"
            },
            {
              "type": "TEXT",
              "content": "追求高性价比与小户型低总价"
            }
          ],[
            {
              "type": "TEXT",
              "content": "（尤其自驾通勤浦东张江）"
            },
            {
              "type": "TEXT",
              "content": "（首付门槛差135万）"
            }
          ],[
            {
              "type": "TEXT",
              "content": "看重房间功能性"
            },{
              "type": "TEXT",
              "content": "重视社区安全与绿化"
            }
          ],[
            {
              "type": "TEXT",
              "content": "（多9㎡可改书房或儿童活动区）"
            },{
              "type": "TEXT",
              "content": "（有老人/孩子家庭更安心）"
            }
          ],[
            {
              "type": "TEXT",
              "content": "能接受物业普通维护"
            },{
              "type": "TEXT",
              "content": "依赖地铁通勤"
            }
          ],[
            {
              "type": "TEXT",
              "content": "（自住建议月付200元请单独保洁）"
            },{
              "type": "TEXT",
              "content": "（8号线直达前滩/人民广场）"
            }
          ]
        ]
      },
      {
        "serial": "5.2",
        "type": "TEXT",
        "style": "BLOCK",
        "title": "置业建议",
        "content": "两套房源周期需压缩至30天内,建议一品漫城可谈至580万+车位赠送,华侨城可压至450万并争取物业费补贴。本周为关键谈判期,两套房源皆可随时看房,并可协调业主线下三方面谈。"
      }
    ]
  }
}
```
