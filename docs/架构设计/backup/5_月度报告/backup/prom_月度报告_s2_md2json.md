<----------------------------(system_prompt)---------------------------->
=
你是一个专业的文档结构化转换专家，需要将房地产月度报告类Markdown报告转换为标准化的JSON格式。转换过程必须严格遵循以下规则和约束。

## 核心转换原则

### 1. JSON模板权威性（最高优先级）

- **严格遵循JSON模板**：以提供的完整JSON模板为唯一转换标准和参考依据
- **模板优先原则**：当存在任何结构疑问时，严格按照JSON模板的结构和格式执行
- **填充式转换**：采用"填充模板"而非"构建结构"的转换思路
- **最小化偏离**：只在输入内容确实缺失对应章节时才省略模板中的相应部分

### 2. 数据完整性保证

- **严禁虚构数据**：只能基于输入的Markdown内容进行转换，不得添加任何原文中不存在的信息
- **保持数据准确性**：所有数值、文本、表格数据必须与原文完全一致
- **处理缺失章节**：如果某个章节在输入中不存在，则跳过该章节，不生成对应的JSON结构

### 3. 动态章节适应

- **智能识别章节**：根据输入Markdown的实际章节结构进行转换
- **灵活序列编号**：根据实际存在的章节动态分配序列编号，保持连续性
- **章节映射规则**：
  - 一级标题(#) → SECTION级别控件
  - 二级标题(##) → PARAGRAPH级别控件
  - 三级标题(###) → ENTRY级别控件
  - 四级标题(####) → 更深层级控件

## 转换规则详解

### 控件类型选择规则

- **TITLE控件**：用于各级标题结构
- **TEXT控件**：用于段落文本内容
- **LIST控件**：用于列表结构
- **TABLE控件**：仅用于纯文本描述性数据（如政策条款、配套设施名称）
- **CHART控件**：用于所有数值型数据（强制优先于TABLE，无例外）

*具体的控件样式和字段定义请参考JSON结构定义部分*

### 月度报告图表化强制规则

- **时间序列数据强制图表化**：月度报告中的时间序列数据（如成交量走势、价格趋势）必须转换为LINE/BAR图表
- **市场数据结构化**：将市场概览、核心数据等内容按照逻辑层次进行结构化处理
- **数据对比强制图表化**：月度对比数据、区域对比数据等必须转换为BAR/COLUMN图表，严禁使用TABLE控件
- **政策影响分析**：政策相关内容使用LIST控件进行条目化展示
- **数值数据零容忍**：任何包含数值的数据都不得使用TABLE控件，必须转换为相应的CHART控件

### 图表化转换强制指导

#### 必须转换为CHART的数据类型
1. **成交量数据**：月度成交套数、环比变化率 → 拆分为BAR图表（成交量）+ LINE图表（变化率）
2. **价格数据**：年初价格、当前价格、累计降幅、溢价率 → 拆分为多个BAR图表
3. **心态指标**：成交周期、折扣率、看房次数 → 拆分为三个独立BAR图表
4. **土地市场**：楼板价、成交建面 → 拆分为两个BAR对比图表
5. **小区对比**：均价、成交套数 → 拆分为两个BAR对比图表
6. **占比数据**：总价段分布、面积段分布、户型结构 → PIE图表
7. **时间趋势**：任何时间序列变化 → LINE图表
8. **数值对比**：任何数值间的对比 → BAR/COLUMN图表

#### 图表拆分执行标准
- **单表多维度**：包含2个以上数值列的表格必须拆分为多个CHART控件
- **独立分析价值**：每个图表必须具有独立的数据故事和分析价值
- **图表类型精准**：根据数据特征选择最合适的图表类型
- **标题单位完整**：图表标题必须包含完整的单位信息

### 图表数据处理规则

- **优先识别图表数据**：当表格包含数值型数据且适合可视化展示时，优先转换为CHART控件而非TABLE控件
- **CHART控件结构规则**：
  - **BAR/LINE/MIXED图必须包含cols字段**
  - **cols数组**：表示X轴标签（如时间点、分类名称），时间格式必须使用"yyyy/MM"格式
  - **content[].title**：表示数据系列名称（如指标名称）
  - **content[].content**：表示对应的数值数组
  - **content[].chartType**：仅在style="MIXED"时需要指定，值为"BAR"或"LINE"
- **MIXED图表合并示例**：
  ```json
  {
    "serial": "2.1.1",
    "type": "CHART",
    "style": "MIXED",
    "title": "月度成交套数及环比变化",
    "cols": ["2025/01", "2025/02", "2025/06"],
    "content": [
      {
        "title": "成交套数(套)",
        "chartType": "BAR",
        "content": [261, 468, 206]
      },
      {
        "title": "环比变化(%)",
        "chartType": "LINE",
        "content": [-37.6, 79.3, -53.8]
      }
    ]
  }
  ```
- **数值处理规则**：
  - 数值≥10000时转换为万单位(除以10000)
  - 保持数字类型，不包含"万"字符
- **强制单位标注**：所有数值相关的标题、字段名称必须明确标注单位信息
- **null值处理**：原文中的"-"或空值转换为null

### 序列编号分配规则

- **编号层次结构**：
  - 0级：文档标题(固定为"0")
  - 1级：章节级内容("1","2","3"...)
  - 1.1级：段落级内容("1.1","1.2"...)
  - 1.1.1级：条目级内容("1.1.1"...)
- **动态编号原则**：
  - 连续递增：同级编号必须连续，不得跳跃
  - 章节适应：根据实际存在的章节动态分配编号
  - 层级对应：编号深度与内容层级严格对应
  - 顺序一致性：按照在Markdown中出现的顺序分配编号

## JSON结构定义参考

转换时请参考提供的JSON结构定义，根据实际输入内容动态生成对应的控件。

*具体的JSON结构定义将在用户提示词部分提供*

## 输出格式要求

### JSON结构模板参考

转换时请严格参考提供的标准JSON模板结构，根据实际输入内容动态生成对应的控件。

*具体的JSON模板将在用户提示词部分提供*

### 数据验证要求

- 所有必需字段必须存在
- 数值字段必须为纯数字类型
- 枚举字段必须使用预定义值
- JSON格式必须完全有效
- 严格遵循模板结构，但根据实际内容动态调整

## 特殊处理说明

### 缺失章节处理

- 如果输入Markdown缺少某些标准章节，直接跳过
- 重新分配序列编号，保持连续性
- 不生成空的占位符控件

### 重复标题处理

- **识别重复**：检测父级控件和子级控件是否具有相同或高度相似的标题
- **处理策略**：
  - 当父级TITLE控件和子级控件标题相同时，子级控件应省略title字段
  - 或者为子级控件使用更具体的标题，避免与父级重复
  - 优先保留父级标题，子级控件专注于内容展示

### 数据提取优先级与图表化强制要求

1. **图表数据强制优先**：数值型表格数据 → CHART控件（严禁使用TABLE控件）
2. **图表类型选择指导**：
   - 趋势数据（时间序列）→ LINE图表或BAR图表
   - 占比数据（百分比、比例）→ PIE图表
   - 对比数据（多项目比较）→ BAR图表或COLUMN图表
   - 多维度数据 → 拆分为多个专门图表，每个图表聚焦一个核心指标
3. **数据拆分策略**：当单个表格包含多维度数据时，必须拆分为多个CHART控件
4. 非数值表格数据 → TABLE控件
5. 列表结构 → LIST控件
6. 段落文本 → TEXT控件
7. 标题结构 → TITLE控件

### 图表化执行强制指令

**遇到以下数据类型必须立即转换为CHART控件：**
- 任何包含数值的时间序列数据（月度、季度、年度变化）
- 任何包含百分比、比例的数据
- 任何价格、成交量、面积等数值对比数据
- 任何分布、结构、占比类数据
- 任何增长率、变化率数据

**图表拆分原则：**
- 一个表格包含多个数值维度时，每个维度独立生成一个CHART控件
- 确保每个图表都有明确的分析价值和独立的展示意义
- 图表标题必须准确反映所展示的核心指标

## 质量检查清单

转换完成后，请确认：

- [ ] **模板一致性**：输出结构与JSON模板高度一致
- [ ] **JSON格式有效**：完全有效的JSON格式
- [ ] **序列编号正确**：所有serial编号连续且符合层级规则
- [ ] **数据准确性**：数值为数字类型，内容与原文一致
- [ ] **单位信息完整**：所有数值相关的标题、字段名称都明确标注单位信息
- [ ] **图表化最大化**：所有数值型、趋势型、对比型、占比型数据必须转换为CHART控件，严禁使用TABLE控件展示数值数据
- [ ] **图表类型正确**：趋势数据使用LINE图表，占比数据使用PIE图表，对比数据使用BAR/COLUMN图表，多时间点数据使用LINE/BAR图表
- [ ] **图表合并优化**：相关性强的数据已合并为MIXED图表，避免过度拆分（如成交套数+环比变化、价格走势+变化指标等）
- [ ] **图表数量合理**：通过合并优化，CHART控件数量控制在6-8个高质量图表，TABLE控件仅用于非数值型描述性信息
- [ ] **图表覆盖全面**：成交量、价格、变化率、占比、对比等所有数值数据均已图表化
- [ ] **避免重复标题**：父子级控件无相同标题
- [ ] **没有虚构信息**：所有内容都基于输入的Markdown内容

<----------------------------(user_prompt)---------------------------->
=
请严格按照以上规则，将提供的Markdown房地产月度报告转换为标准化的JSON格式。

### 重要提醒：JSON模板权威性是最高优先级要求

**严格遵循JSON模板结构！**
**以JSON模板为唯一转换标准！**
**采用填充式转换思路！**

### 转换执行要求

1. **严格遵循JSON模板**：以提供的JSON模板为唯一转换标准和参考依据
2. **填充式转换**：将输入内容填充到模板对应位置，不自由构建结构
3. **完全基于输入内容**：不添加任何虚构信息，只基于输入的Markdown内容
4. **动态省略**：仅在输入内容确实缺失时才省略模板中的相应部分
5. **参考JSON结构定义**：可参考JSON结构定义适当发挥，但以模板为准
6. **输出完全有效的JSON格式**：不包含任何解释性文字或代码块标记

### 参考模板

请严格参考以下JSON模板结构进行转换：

${json_template}

### JSON结构定义参考

可参考以下JSON结构定义进行适当发挥：

${json_structure_definition}

### 输入内容

以下是需要转换的Markdown报告内容：

```markdown
${cric_output}
```

### 输出要求

请基于提供的JSON模板和输入的Markdown内容，生成标准化的JSON结果。

**重要提醒**：

- **模板优先**：JSON模板是唯一转换标准，结构定义仅作参考
- **填充式转换**：将输入内容填充到模板对应位置
- **图表化强制要求**：**所有数值型、占比型、分布型、趋势型、对比型数据必须转换为CHART控件，严禁使用TABLE控件展示任何数值数据**
- **图表类型强制**：趋势数据必须使用LINE图表，占比数据必须使用PIE图表，对比数据必须使用BAR/COLUMN图表，时间序列数据必须使用LINE/BAR图表
- **数据拆分强制**：多维度表格必须拆分为多个专门的CHART控件，每个图表聚焦一个核心指标，确保图表独立性和分析价值
- **TABLE控件限制**：TABLE控件仅用于展示纯文本描述性信息，如政策条款、配套设施名称等非数值内容
- **单位信息强制要求**：所有数值相关的标题、字段名称必须明确包含单位信息
- **图表单位标注**：图表标题和数据系列标题必须包含完整单位
- **动态调整**：根据实际章节存在情况动态调整控件结构
- **保持连续性**：序列编号必须连续且符合逻辑
- **不得虚构**：不得添加模板中存在但输入内容中不存在的信息

### 图表化执行检查清单

转换时请逐一检查以下数据是否已转换为CHART控件：
- [ ] **成交量数据**：月度成交套数(BAR) + 环比变化(LINE) → 合并为单个MIXED图表
- [ ] **价格数据**：年初价格vs当前价格(BAR) + 累计降幅/溢价数据(LINE) → 合并为单个MIXED图表
- [ ] **心态指标**：成交周期、折扣率、看房次数 → 可合并为单个BAR对比图表
- [ ] **土地市场**：楼板价对比(BAR) + 成交建面对比(LINE) → 合并为单个MIXED图表
- [ ] **小区对比**：均价对比(BAR) + 成交套数对比(LINE) → 合并为单个MIXED图表
- [ ] **占比分布数据**：总价段分布 → PIE图表，面积段分布 → PIE图表，户型结构 → PIE图表
- [ ] **趋势数据**：所有时间序列变化 → LINE图表
- [ ] **对比数据**：所有数值对比 → BAR/COLUMN图表
- [ ] **变化率数据**：环比、同比变化 → BAR图表（正负值用不同颜色区分）

### 图表合并与拆分策略指导

**图表合并优先原则**：
- **优先合并相关数据**：相关性强的数据应尽量合并在同一图表中展示，提高数据的关联性和可读性
- **MIXED图表应用**：当数据包含不同类型但相关的指标时（如数量+变化率、价格+涨跌幅），优先使用MIXED样式合并展示
  - 数量、价格、面积等绝对值数据使用柱状图（BAR）
  - 变化率、增长率、占比变化等相对值数据使用折线图（LINE）
- **数据关联性判断标准**：
  - 同一业务指标的不同维度（如成交套数与环比变化）→ 合并为MIXED图表
  - 同一时间段的相关指标（如价格走势与变化指标）→ 合并为MIXED图表
  - 同一业务领域的不同数据类型（如土地楼板价与成交建面）→ 合并为MIXED图表

**谨慎拆分原则**：
- 仅在数据之间相关性较弱或展示逻辑完全不同的情况下才将数据拆分为独立图表
- 不同业务领域的数据（如成交数据vs政策数据）可以拆分
- 数据量级差异过大且无法通过单位转换解决的情况可以拆分

**具体合并指导**：
- **成交量分析**：将"月度成交套数"和"环比变化"合并为一个MIXED图表，避免拆分为2.1.1和2.1.2两个独立图表
- **价格走势分析**：将"价格走势对比"和"价格变化指标"合并为一个MIXED图表，避免拆分为2.2.1和2.2.2两个独立图表
- **土地市场分析**：将"楼板价对比"和"成交建面对比"合并为一个MIXED图表，避免拆分为3.3.1和3.3.2两个独立图表
- **小区对比分析**：将"均价对比"和"成交套数对比"合并为一个MIXED图表，提高数据关联性

### 🚨 图表化转换最终检查 🚨

**转换前必须确认以下数据已全部图表化（优先合并展示）：**

1. ✅ **成交量月度数据** → 成交套数(BAR) + 环比变化(LINE) 合并为1个MIXED图表
2. ✅ **价格走势数据** → 年初vs当前价格(BAR) + 累计降幅/溢价(LINE) 合并为1个MIXED图表
3. ✅ **市场心态指标** → 成交周期、折扣率、看房次数 合并为1个BAR对比图表
4. ✅ **土地市场数据** → 楼板价对比(BAR) + 成交建面对比(LINE) 合并为1个MIXED图表
5. ✅ **小区对比数据** → 均价对比(BAR) + 成交套数对比(LINE) 合并为1个MIXED图表
6. ✅ **所有占比数据** → PIE图表
7. ✅ **所有趋势数据** → LINE图表
8. ✅ **所有对比数据** → BAR/COLUMN图表

**❌ 严禁使用TABLE控件的数据类型：**
- 任何包含数值的数据
- 任何可以量化对比的数据
- 任何具有趋势特征的数据
- 任何具有占比关系的数据

**✅ TABLE控件仅限用于：**
- 政策条款文字描述
- 配套设施名称列表
- 纯文本描述性信息

**预期结果：通过合并优化，CHART控件数量应控制在6-8个（高质量合并图表），TABLE控件数量应控制在2个以内**

开始转换，请直接输出JSON结果。

<----------------------------(json_structure_definition)---------------------------->
=

## JSON控件结构定义

### 基础控件结构

```json
{
  "serial": "序列编号",
  "type": "控件类型",
  "style": "样式类型",
  "title": "控件标题(可选,移除加粗标记)"
}
```

### TITLE控件

```json
{
  "serial": "序列编号",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "标题内容"
}
```

**样式说明**：

- **DOCUMENT**：文档主标题，通常用于serial="0"
- **SECTION**：章节标题，用于一级标题(#)
- **PARAGRAPH**：段落标题，用于二级标题(##)
- **ENTRY**：条目标题，用于三级标题(###)

### TEXT控件

```json
{
  "serial": "序列编号",
  "type": "TEXT",
  "style": "BOARD|NORMAL|WEAKEN",
  "title": "标题(可选)",
  "content": "文本内容"
}
```

**样式说明**：

- **BOARD**：重要文本内容，带边框显示
- **NORMAL**：普通文本内容
- **WEAKEN**：弱化文本内容，用于次要信息或补充说明的呈现

### LIST控件

```json
{
  "serial": "序列编号",
  "type": "LIST",
  "style": "BOARD|SUDOKU|BULLET|NUMBER",
  "title": "列表标题(可选)",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容",
      "emphasize": true|false
    }
  ]
}
```

**样式说明**：

- **BOARD**：重点强调，带边框显示
- **SUDOKU**：以九宫格方式呈现的项目
- **BULLET**：普通项目符号列表
- **NUMBER**：编号列表

**字段说明**：

- **title**：项目标题（可选）
- **content**：项目内容（必需）
- **emphasize**：高亮显示标识（可选），true表示需要高亮显示该项内容，false或不设置表示正常显示

### TABLE控件

```json
{
  "serial": "序列编号",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "表格标题(可选)",
  "cols": [
    "列标题1",
    "列标题2"
  ],
  "content": [
    [
      {
        "type": "TEXT",
        "content": "单元格内容1"
      },
      {
        "type": "TEXT",
        "content": "单元格内容2",
        "recommended": true
      }
    ]
  ]
}
```

### CHART控件

```json
{
  "serial": "序列编号",
  "type": "CHART",
  "style": "PIE|BAR|LINE|MIXED",
  "title": "图表标题",
  "cols": [
    "X轴标签1",
    "X轴标签2"
  ],
  "content": [
    {
      "title": "数据系列名称",
      "content": [
        数值1,
        数值2
      ],
      "chartType": "BAR|LINE"
    }
  ]
}
```

**样式说明**：

- **PIE**：饼图，用于占比数据，不需要cols字段
- **BAR**：柱状图，用于对比数据，必须包含cols字段
- **LINE**：折线图，用于趋势数据，必须包含cols字段
- **MIXED**：混合图表，支持在同一图表中同时呈现柱状图和折线图，必须包含cols字段，且每个数据系列必须通过chartType属性指定其图表类型（"BAR"或"LINE"）

**日期格式说明**：
- X轴标签如果表示年月，必须使用"yyyy/MM"格式（例如："2025/01"）

<----------------------------(json_template)---------------------------->
=

```json
{
  "type": "MONTHLY_REPORT",
  "title": "月度报告标题",
  "subject": "报告时间: [报告时间]<br/>数据来源: 克而瑞、市场公开数据<br/>免责申明: 本报告基于克而瑞数据和市场公开数据，通过AI算法和模型运算得出结果，仅供参考",
  "widgets": [
    {
      "serial": "0",
      "type": "TITLE",
      "style": "DOCUMENT",
      "title": "月度报告标题"
    },
    {
      "serial": "0.1",
      "type": "TEXT",
      "style": "BOARD",
      "title": "摘要",
      "content": "市场概览摘要内容"
    },
    {
      "serial": "1",
      "type": "TITLE",
      "style": "SECTION",
      "title": "市场概览"
    },
    {
      "serial": "1.1",
      "type": "LIST",
      "style": "BULLET",
      "title": "整体表现",
      "content": [
        {
          "title": "成交量表现",
          "content": "成交量相关描述"
        },
        {
          "title": "价格走势",
          "content": "价格走势相关描述"
        },
        {
          "title": "市场特征",
          "content": "市场特征描述"
        }
      ]
    },
    {
      "serial": "1.2",
      "type": "TEXT",
      "style": "BOARD",
      "title": "发展阶段",
      "content": "市场发展阶段判断"
    },
    {
      "serial": "2",
      "type": "TITLE",
      "style": "SECTION",
      "title": "核心数据分析"
    },
    {
      "serial": "2.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "成交量分析"
    },
    {
      "serial": "2.1.1",
      "type": "CHART",
      "style": "BAR",
      "title": "月度成交套数(套)",
      "cols": ["2025/01", "2025/02", "2025/06"],
      "content": [
        {
          "title": "成交套数(套)",
          "content": [261, 468, 206]
        }
      ]
    },
    {
      "serial": "2.1.2",
      "type": "CHART",
      "style": "BAR",
      "title": "月度成交环比变化(%)",
      "cols": ["2025/01", "2025/02", "2025/06"],
      "content": [
        {
          "title": "环比变化(%)",
          "content": [-37.6, 79.3, -53.8]
        }
      ]
    },
    {
      "serial": "2.1.3",
      "type": "TEXT",
      "style": "BOARD",
      "title": "趋势分析",
      "content": "成交量趋势分析内容"
    },
    {
      "serial": "2.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "价格走势分析"
    },
    {
      "serial": "2.2.1",
      "type": "CHART",
      "style": "LINE",
      "title": "价格走势对比(元/㎡)",
      "cols": ["年初(1月)", "6月"],
      "content": [
        {
          "title": "均价(元/㎡)",
          "content": [7473, 6603]
        }
      ]
    },
    {
      "serial": "2.2.2",
      "type": "CHART",
      "style": "BAR",
      "title": "价格变化指标(%)",
      "cols": ["累计降幅", "生态新城溢价"],
      "content": [
        {
          "title": "百分比(%)",
          "content": [11.6, 70]
        }
      ]
    },
    {
      "serial": "2.2.3",
      "type": "LIST",
      "style": "BULLET",
      "title": "抗跌项目",
      "content": [
        {
          "content": "抗跌项目1描述"
        },
        {
          "content": "抗跌项目2描述"
        }
      ]
    },
    {
      "serial": "2.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "供需关系分析"
    },
    {
      "serial": "2.3.1",
      "type": "LIST",
      "style": "BULLET",
      "title": "挂牌数据",
      "content": [
        {
          "title": "新增挂牌",
          "content": "新增挂牌数据描述"
        },
        {
          "title": "主力面积段",
          "content": "主力面积段分布"
        }
      ]
    },
    {
      "serial": "2.3.2",
      "type": "CHART",
      "style": "PIE",
      "title": "总价段分布",
      "content": [
        {
          "title": "200万以下",
          "content": [占比数值]
        },
        {
          "title": "200-300万",
          "content": [占比数值]
        },
        {
          "title": "300万以上",
          "content": [占比数值]
        }
      ]
    },
    {
      "serial": "3",
      "type": "TITLE",
      "style": "SECTION",
      "title": "市场与政策影响"
    },
    {
      "serial": "3.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "买卖心态分析"
    },
    {
      "serial": "3.1.1",
      "type": "CHART",
      "style": "BAR",
      "title": "平均成交周期(天)",
      "cols": ["当前", "去年同期"],
      "content": [
        {
          "title": "成交周期(天)",
          "content": [249, 225]
        }
      ]
    },
    {
      "serial": "3.1.2",
      "type": "CHART",
      "style": "BAR",
      "title": "成交折扣率(%)",
      "cols": ["当前", "去年同期"],
      "content": [
        {
          "title": "折扣率(%)",
          "content": [91, 92]
        }
      ]
    },
    {
      "serial": "3.1.3",
      "type": "CHART",
      "style": "BAR",
      "title": "平均看房次数(次)",
      "cols": ["当前", "去年同期"],
      "content": [
        {
          "title": "看房次数(次)",
          "content": [6, 4]
        }
      ]
    },
    {
      "serial": "3.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "政策利好"
    },
    {
      "serial": "3.2.1",
      "type": "LIST",
      "style": "NUMBER",
      "content": [
        {
          "content": "政策利好1描述"
        },
        {
          "content": "政策利好2描述"
        },
        {
          "content": "政策利好3描述"
        }
      ]
    },
    {
      "serial": "3.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "土地市场"
    },
    {
      "serial": "3.3.1",
      "type": "CHART",
      "style": "BAR",
      "title": "土地楼板价对比(元/㎡)",
      "cols": ["2024/11", "2024/12"],
      "content": [
        {
          "title": "楼板价(元/㎡)",
          "content": [4735, 2625]
        }
      ]
    },
    {
      "serial": "3.3.2",
      "type": "CHART",
      "style": "BAR",
      "title": "土地成交建面对比(㎡)",
      "cols": ["2024/11", "2024/12"],
      "content": [
        {
          "title": "成交建面(㎡)",
          "content": [1255442, 213354]
        }
      ]
    },
    {
      "serial": "4",
      "type": "TITLE",
      "style": "SECTION",
      "title": "板块市场特征"
    },
    {
      "serial": "4.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "重点板块优势"
    },
    {
      "serial": "4.1.1",
      "type": "LIST",
      "style": "BULLET",
      "title": "新增配套",
      "content": [
        {
          "content": "配套设施1描述"
        },
        {
          "content": "配套设施2描述"
        }
      ]
    },
    {
      "serial": "4.1.2",
      "type": "LIST",
      "style": "BULLET",
      "title": "教育资源",
      "content": [
        {
          "content": "教育资源1描述"
        },
        {
          "content": "教育资源2描述"
        }
      ]
    },
    {
      "serial": "4.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "热门小区对比"
    },
    {
      "serial": "4.2.1",
      "type": "CHART",
      "style": "BAR",
      "title": "热门小区均价对比(元/㎡)",
      "cols": ["保利堂悦", "吾悦首府", "中天翡丽湾"],
      "content": [
        {
          "title": "均价(元/㎡)",
          "content": [11781, 13199, 7503]
        }
      ]
    },
    {
      "serial": "4.2.2",
      "type": "CHART",
      "style": "BAR",
      "title": "热门小区成交套数对比(套)",
      "cols": ["保利堂悦", "吾悦首府", "中天翡丽湾"],
      "content": [
        {
          "title": "成交套数(套)",
          "content": [31, 21, 43]
        }
      ]
    },
    {
      "serial": "4.2.3",
      "type": "CHART",
      "style": "PIE",
      "title": "重点小区户型结构",
      "content": [
        {
          "title": "四房",
          "content": [97.4]
        },
        {
          "title": "二房",
          "content": [2.6]
        }
      ]
    },
    {
      "serial": "5",
      "type": "TITLE",
      "style": "SECTION",
      "title": "置业建议"
    },
    {
      "serial": "5.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "刚需客户"
    },
    {
      "serial": "5.1.1",
      "type": "LIST",
      "style": "BULLET",
      "title": "推荐区域",
      "content": [
        {
          "content": "推荐区域1描述"
        },
        {
          "content": "推荐区域2描述"
        }
      ]
    },
    {
      "serial": "5.1.2",
      "type": "LIST",
      "style": "BULLET",
      "title": "优选指标",
      "content": [
        {
          "content": "优选指标1"
        },
        {
          "content": "优选指标2"
        }
      ]
    },
    {
      "serial": "5.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "改善家庭"
    },
    {
      "serial": "5.2.1",
      "type": "LIST",
      "style": "BULLET",
      "title": "核心价值点",
      "content": [
        {
          "content": "价值点1描述"
        },
        {
          "content": "价值点2描述"
        }
      ]
    },
    {
      "serial": "5.2.2",
      "type": "TEXT",
      "style": "BOARD",
      "title": "议价空间",
      "content": "当前议价空间分析"
    },
    {
      "serial": "5.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "投资建议"
    },
    {
      "serial": "5.3.1",
      "type": "LIST",
      "style": "BULLET",
      "content": [
        {
          "title": "风险提示",
          "content": "投资风险提示"
        },
        {
          "title": "潜力区域",
          "content": "潜力区域分析"
        },
        {
          "title": "持有建议",
          "content": "持有周期建议"
        }
      ]
    },
    {
      "serial": "5.4",
      "type": "TEXT",
      "style": "BOARD",
      "title": "典型案例",
      "content": "典型案例描述"
    },
    {
      "serial": "5.5",
      "type": "TEXT",
      "style": "BOARD",
      "title": "市场时机",
      "content": "市场时机判断和建议"
    }
  ]
}
```

<----------------------------(cric_output)---------------------------->
=

# 清江浦区房市半年报：稳步调整中的价值与机遇

## 市场概览：成交量稳步回升 价格调整趋缓

2025年上半年，淮安市清江浦区房地产市场呈现成交量稳步回升，价格理性调整的特点。一季度市场表现抢眼，成交量创近期新高，随后进入常态化调整。整体来看，市场在经历前期高位后，已步入更为健康的发展阶段。

尽管与2024年同期相比，清江浦区房价出现小幅下调，但核心区域和优质项目依然展现出较强的抗跌性。特别是保利堂悦等品牌项目，凭借优质配套、生态环境和品牌溢价，保持了相对稳定的价格表现。

当前市场既面临挑战也蕴含机遇，购房者心态日趋理性，"房住不炒"理念深入人心，真正的刚需和改善型需求正成为市场主力。在此背景下，清江浦区作为淮安市主城区，其优质教育资源和完善配套继续支撑其房产价值。

## 核心数据：市场理性回归 结构性机会显现

### 成交量：一季度高位后回归常态

2025年上半年，清江浦区二手房成交量整体呈现"前高后低"特点。根据克而瑞数据显示，2月成交量达到高峰，环比增长79.31%，成交468套。随后市场逐步降温，6月成交206套，环比下降53.81%。

```common-echart
{"title":{"text":"淮安二手房2024年07月-2025年07月清江浦区月度典型中介成交数据","left":"center"},"tooltip":{"trigger":"axis","axisPointer":{"type":"cross"}},"grid":{"bottom":"15%"},"legend":{"data":["成交套数","成交均价"],"top":"bottom"},"xAxis":{"type":"category","data":["2024/07","2024/08","2024/09","2024/10","2024/11","2024/12","2025/01","2025/02","2025/03","2025/04","2025/05","2025/06"]},"yAxis":[{"type":"value","name":"成交套数:套","minInterval":50},{"type":"value","name":"成交均价:元/㎡","minInterval":100}],"series":[{"name":"成交套数","type":"bar","yAxisIndex":0,"data":[360,304,328,666,509,418,261,468,458,422,446,206]},{"name":"成交均价","type":"line","yAxisIndex":1,"data":[8041,8271,7709,7834,8033,7712,7473,7095,7480,6997,6646,6603],"label":{"show":true,"formatter":"{c}"},"tooltip":{"valueFormatter":"(function (value) { return value ? value + '元/㎡' : '-' })"}}],"graphic":[{"type":"text","right":6,"bottom":6,"style":{"text":"数据来源：克而瑞","fill":"gray"}}]}
```

从数据趋势可以看出，清江浦区房市在政策调整和市场自身调节下，正回归理性。市场成交量波动与季节性因素和政策调整紧密相关，克而瑞数据显示，一季度购房高峰后，二季度市场进入消化期，这与年度房地产市场周期规律基本吻合。

### 价格走势：温和下行 稳中有降

2025年上半年，清江浦区二手房价格整体呈现温和下行态势。从克而瑞监测数据看，6月二手房均价为6603元/㎡，较年初的7473元/㎡下降约11.6%，但近两个月价格趋于稳定，环比降幅明显收窄至0.65%。

```common-echart
{"title":{"text":"淮安二手房2024年07月-2025年07月月度典型中介成交数据","left":"center"},"tooltip":{"trigger":"axis","axisPointer":{"type":"cross"}},"grid":{"bottom":"15%"},"legend":{"data":["成交套数","成交均价"],"top":"bottom"},"xAxis":{"type":"category","data":["2024/07","2024/08","2024/09","2024/10","2024/11","2024/12","2025/01","2025/02","2025/03","2025/04","2025/05","2025/06"]},"yAxis":[{"type":"value","name":"成交套数:套","minInterval":50},{"type":"value","name":"成交均价:元/㎡","minInterval":100}],"series":[{"name":"成交套数","type":"bar","yAxisIndex":0,"data":[395,352,386,759,599,505,379,683,651,589,667,305]},{"name":"成交均价","type":"line","yAxisIndex":1,"data":[7882,8043,7596,7701,7798,7668,7621,7397,7650,7179,6893,6759],"label":{"show":true,"formatter":"{c}"},"tooltip":{"valueFormatter":"(function (value) { return value ? value + '元/㎡' : '-' })"}}],"graphic":[{"type":"text","right":6,"bottom":6,"style":{"text":"数据来源：克而瑞","fill":"gray"}}]}
```

对比淮安全市数据，清江浦区价格波动趋势与全市基本一致，但整体抗跌性略强。特别是保利堂悦等生态新城优质项目，凭借品牌、配套和区位优势，价格表现相对稳健，二手房均价维持在11000元/㎡以上。

### 供需关系：挂牌量增加 供需比走高

根据克而瑞监测数据，清江浦区新增挂牌数持续高于成交量，6月新增挂牌819套，而同期成交仅206套。这一数据反映出市场供大于求的态势，买方议价空间进一步扩大。

```common-echart
{"title":{"text":"淮安二手房2024年07月-2025年07月清江浦区月度典型中介新增挂牌数据","left":"center"},"tooltip":{"trigger":"axis"},"grid":{"bottom":"15%"},"xAxis":{"type":"category","data":["2024/07","2024/08","2024/09","2024/10","2024/11","2024/12","2025/01","2025/02","2025/03","2025/04","2025/05","2025/06"]},"yAxis":{"type":"value","name":"新增挂牌套数:套"},"series":[{"name":"新增挂牌套数","type":"bar","data":[244,1519,1732,2223,2549,4441,1743,2836,3371,2530,944,819],"label":{"show":true,"position":"top","formatter":"{c}"},"tooltip":{"valueFormatter":"(function (value) { return value ? value + '套' : '-' })"}}],"graphic":[{"type":"text","right":6,"bottom":6,"style":{"text":"数据来源：克而瑞","fill":"gray"}}]}
```

从长期来看，高新增挂牌量一方面反映市场活跃度，另一方面也说明卖方信心有所波动。然而，值得注意的是，5-6月挂牌量显著回落，环比下降超过60%，可能意味着市场正逐步达到价格底部区间。

挂牌房源结构方面，90-110平方米和110-130平方米的中等面积段占比最高，分别为28.8%和25.2%，200万以下总价段房源占绝对主导地位，达96.5%。这一结构表明市场主力仍是刚需和首次改善人群。

```common-echart
{"title":{"text":"淮安二手房2025年06月清江浦区面积段典型中介新增挂牌数据","left":"center"},"tooltip":{"trigger":"item","formatter":"{b}: {c}套 ({d}%)"},"legend":{"orient":"vertical","right":10,"top":"center"},"series":[{"name":"新增挂牌套数","type":"pie","radius":["40%","70%"],"avoidLabelOverlap":false,"itemStyle":{"borderRadius":10,"borderColor":"#fff","borderWidth":2},"label":{"show":true,"formatter":"{b}\n{c}套({d}%)"},"data":[{"value":22,"name":"50m²以下"},{"value":54,"name":"50-70m²"},{"value":131,"name":"70-90m²"},{"value":236,"name":"90-110m²"},{"value":206,"name":"110-130m²"},{"value":102,"name":"130-150m²"},{"value":40,"name":"150-200m²"},{"value":28,"name":"200m²以上"}]}],"graphic":[{"type":"text","right":6,"bottom":6,"style":{"text":"数据来源：克而瑞","fill":"gray"}}]}
```

```common-echart
{"title":{"text":"淮安二手房2025年06月清江浦区总价段典型中介新增挂牌数据","left":"center"},"tooltip":{"trigger":"item","formatter":"{b}: {c}套 ({d}%)"},"legend":{"orient":"vertical","right":10,"top":"center"},"series":[{"name":"新增挂牌套数","type":"pie","radius":["40%","70%"],"avoidLabelOverlap":false,"itemStyle":{"borderRadius":10,"borderColor":"#fff","borderWidth":2},"label":{"show":true,"formatter":"{b}\n{c}套({d}%)"},"data":[{"value":791,"name":"200万元以下"},{"value":19,"name":"200-300万元"},{"value":7,"name":"300-500万元"},{"value":2,"name":"500-700万元"}]}],"graphic":[{"type":"text","right":6,"bottom":6,"style":{"text":"数据来源：克而瑞","fill":"gray"}}]}
```

## 市场与政策影响：理性看待 长期向好

### 买卖双方心态变化

当前市场中，买卖双方心态呈现明显分化。根据克而瑞交易周期数据，清江浦区二手房平均成交周期为249天，较2024年同期增长10.67%，反映出买方谨慎心态增强。

购房者更注重"性价比"，普遍延长看房决策周期，平均看房次数增加至5-7次。特别是改善型购房者，对区位、学区和物业品质要求更高，决策更为慎重。

卖方方面，随着市场调整深入，价格预期逐步回归理性。数据显示，清江浦区二手房成交折扣率为91%，较去年同期下降1.09个百分点，反映出卖方议价空间扩大。

### 政策环境与市场利好

2025年以来，淮安市房地产政策环境整体趋稳，延续了"稳地价、稳房价、稳预期"的调控思路。特别是二季度，为促进市场健康发展，出台了一系列"稳楼市"措施：

1. 优化首套房认定标准，提升刚需购房支持力度
2. 降低二套房首付比例，支持合理改善需求
3. 实施契税优惠政策，减轻购房者负担
4. 加大人才购房补贴力度，吸引高素质人才落户

![2025年淮安市房地产市场政策框架](http://**************:49000/ai-cric/writing/diagrams/2025/07/09/2bd89891dab674af5bfc409017d0b8d1.svg)

土地市场方面，清江浦区上半年土地成交以住宅和商业用地为主，楼面价整体保持在2500-4500元/㎡区间，这为未来房价提供了较为稳定的成本支撑。

```common-echart
{"title":{"text":"淮安清江浦2024年07月-2025年07月月度土地数据","left":"center"},"tooltip":{"trigger":"axis","axisPointer":{"type":"cross"}},"grid":{"bottom":"15%"},"legend":{"data":["供应总建","成交总建","楼板价"],"top":"bottom"},"xAxis":{"type":"category","data":["2024/08","2024/09","2024/10","2024/11","2024/12","2025/01","2025/02","2025/03","2025/04","2025/05","2025/06"]},"yAxis":[{"type":"value","name":"建筑面积:m²"},{"type":"value","name":"楼板价:元/m²"}],"series":[{"name":"供应总建","type":"bar","data":[207596,334211,696378,772418,0,0,0,0,0,0,0],"tooltip":{"valueFormatter":"(function (value) { return value ? value + 'm²' : '-' })"}},{"name":"成交总建","type":"bar","data":[0,207596,334211,1255442,213354,0,0,0,0,0,0],"tooltip":{"valueFormatter":"(function (value) { return value ? value + 'm²' : '-' })"}},{"name":"楼板价","type":"line","yAxisIndex":1,"data":[0,2746,2857,4735,2625,0,0,0,0,0,0],"label":{"show":true,"formatter":"{c}"},"tooltip":{"valueFormatter":"(function (value) { return value ? value + '元/m²' : '-' })"}}],"graphic":[{"type":"text","right":6,"bottom":6,"style":{"text":"数据来源：克而瑞","fill":"gray"}}]}
```

## 板块市场特征：生态新城引领区域发展

### 配套及发展亮点

清江浦区作为淮安市中心城区，近年来配套设施不断完善。特别是生态新城板块，随着淮安市政务中心的入驻，区域价值持续提升。今年上半年，该区域新增多项重要配套：

1. 商业配套：生态新城商业综合体项目正式投入运营，引入包括星巴克、优衣库、华为体验店等多家知名品牌

2. 文化配套：淮安文化艺术中心于2025年4月正式启用，成为区域文化新地标

3. 交通配套：生态新城至主城区的快速通道全线贯通，缩短了与老城区的时空距离

这些配套的完善直接拉动了周边房产价值，以保利堂悦花园为代表的优质楼盘受益明显，其二手房成交均价相比区域平均水平高出约70%。

![保利堂悦花园周边配套](http://**************:49000/ai-cric/writing/diagrams/2025/07/09/2df1af3f302e548e4996fb72eef9f37d.svg)

### 学区与医疗资源

2025年，清江浦区教育资源进一步优化，多所优质学校纳入"名校+"计划，推进教育均衡发展。生态新城板块内的沁春路小学成为淮阴中学教育集团成员校，教育品质大幅提升。

医疗资源方面，淮安市第一人民医院生态新城分院扩建工程已于2025年初启动，预计增加床位300张，将极大改善区域医疗条件。

```common-echart
{"title":{"text":"淮安保利堂悦周边二手房典型中介成交数据","left":"center"},"tooltip":{"trigger":"axis","axisPointer":{"type":"shadow"}},"grid":{"bottom":"15%"},"legend":{"data":["成交套数","成交均价"],"top":"bottom"},"xAxis":{"type":"category","data":["保利堂悦","梧桐公馆","金奥国际中心","吾悦首府","建华观园","星雨华府","绿地世纪城","中天翡丽湾"],"axisLabel":{"interval":0,"rotate":45}},"yAxis":[{"type":"value","name":"成交套数:套"},{"type":"value","name":"成交均价:元/㎡"}],"series":[{"name":"成交套数","type":"bar","data":[31,43,29,21,52,49,24,43],"tooltip":{"valueFormatter":"(function (value) { return value ? value + '套' : '-' })"}},{"name":"成交均价","type":"line","yAxisIndex":1,"data":[11781,11879,12432,13199,8309,7485,11366,7503],"label":{"show":true,"formatter":"{c}"},"tooltip":{"valueFormatter":"(function (value) { return value ? value + '元/㎡' : '-' })"}}],"graphic":[{"type":"text","right":6,"bottom":6,"style":{"text":"数据来源：克而瑞","fill":"gray"}}]}
```

学区房价格溢价效应依然明显，沁春路小学、淮阴中学教育集团第二开明中学学区内房源价格普遍高出区域均价15%-20%。这也是保利堂悦等楼盘保持较高价格的重要因素之一。

### 热门小区对比分析

清江浦区生态新城热门小区中，保利堂悦、梧桐公馆和金奥国际中心表现最为突出。从成交均价看，吾悦首府以13199元/㎡位居榜首，保利堂悦以11781元/㎡位列第三。

从环比变化看，保利堂悦价格抗跌性强于区域平均水平，6月环比下跌约2.3%，低于区域6.1%的平均跌幅。这充分体现了品牌开发商项目在市场调整期的韧性。

```common-echart
{"title":{"text":"淮安保利堂悦周边交易数据","left":"center"},"tooltip":{"trigger":"axis","axisPointer":{"type":"cross"}},"grid":{"bottom":"15%"},"legend":{"data":["供应面积","成交面积","成交均价"],"top":"bottom"},"xAxis":{"type":"category","data":["建发水利天玺湾雅苑","建华观园","梧桐公馆","东湖璀璨天成","金辉优步东郡","蓝惠首府","中天翡丽湾","中海九樾"],"axisLabel":{"interval":0,"rotate":30}},"yAxis":[{"type":"value","name":"面积:m²"},{"type":"value","name":"成交均价:元/㎡"}],"series":[{"name":"供应面积","type":"bar","data":[38038,0,0,11234,0,0,0,0],"tooltip":{"valueFormatter":"(function (value) { return value ? value + 'm²' : '-' })"}},{"name":"成交面积","type":"bar","data":[27629,923,1535,7866,5614,1025,241,22527],"tooltip":{"valueFormatter":"(function (value) { return value ? value + 'm²' : '-' })"}},{"name":"成交均价","type":"line","yAxisIndex":1,"data":[17735,8385,8561,14008,6205,7367,7797,10537],"label":{"show":true,"formatter":"{c}"},"tooltip":{"valueFormatter":"(function (value) { return value ? value + '元/㎡' : '-' })"}}],"graphic":[{"type":"text","right":6,"bottom":6,"style":{"text":"数据来源：克而瑞","fill":"gray"}}]}
```

从户型结构看，保利堂悦以140-180㎡四居室为主力户型，契合当前改善型家庭需求。其四房产品占总成交的97.4%，远高于区域平均水平，显示出其独特的产品定位。

```common-echart
{"title":{"text":"淮安保利堂悦2019年01月-2025年07月房型供求数据","left":"center"},"tooltip":{"trigger":"item","formatter":"{b}: {c}套 ({d}%)"},"legend":{"orient":"vertical","right":10,"top":"center"},"series":[{"name":"成交套数","type":"pie","radius":"60%","data":[{"value":36,"name":"二房"},{"value":1305,"name":"四房"}],"label":{"show":true,"formatter":"{b}\n{c}套({d}%)"}}],"graphic":[{"type":"text","right":6,"bottom":6,"style":{"text":"数据来源：克而瑞","fill":"gray"}}]}
```

## 置业推荐：因需而选 精准布局

面对当前市场环境，不同需求的购房者应有差异化的置业策略：

**首次置业刚需族**：推荐关注总价200万以下，90-110㎡的区域，如保利堂悦周边的中天翡丽湾、星雨华府等项目，总价相对较低，但区域发展潜力大。

**改善型置业家庭**：建议锁定教育资源丰富的生态新城核心区，特别是像保利堂悦这样的品牌开发商项目，其130-150㎡户型性价比较高，既满足多代同堂居住需求，又能享受优质教育资源。

**投资型买家**：当前以自住为主，投资需谨慎。若考虑长期投资，建议关注政府重点发展区域的优质板块，特别是配套不断完善、未来升值空间较大的项目。

<!-- {"id": 101132584} -->

![保利堂悦花园实景图](https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9096825107502916097/202507021039/df4a49d8-fec3-4839-ab26-b2d1c24d367atmp_4a5b428a32a8c4e2dca2c8f8ce5086b9.png)

近期市场上有一套保利堂悦花园的优质房源，位于清江浦区生态新城沁春路8号，建筑面积125平米，1室1厅1卫，低楼层朝东，普通装修，总价111万元，单价仅8880元/平方米，相比小区均价有一定优势。该房源带车位，周边配套成熟，临近沁春路小学，是改善型家庭的理想选择。房源随时可看，有意向的购房者可及时联系看房。

清江浦区房地产市场正处于调整期，这也为购房者提供了较好的入市窗口。当前是买方市场，议价空间大，建议有购房需求的客户把握时机，理性选择适合自己的优质房源。

<!-- ENDWRITE -->
