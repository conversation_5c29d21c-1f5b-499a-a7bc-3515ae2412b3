package com.dcai.aixg.api;


import com.dcai.aixg.dto.*;
import com.dcai.aixg.pro.*;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.ejuetc.commons.base.filter.LoginTokenFilter.LOGIN_INFO_ATT;

@Tag(name = "经纪人接口")
@FeignClient(name = "aixg", path = "/aixg", contextId = "brokerAPI")
public interface BrokerAPI {

    @Operation(summary = "WEB_获取微信openid")
    @GetMapping("/web/broker/getWxOpenId")
    ApiResponse<WechatResp.WechatOpenId> getWxOpenId(@RequestParam(name = "code") String code);

    @Operation(summary = "WEB_获取微信手机号")
    @PostMapping("/web/broker/getWxPhone")
    ApiResponse<WechatResp.PhoneNumber> getWxPhone(@RequestBody @Valid WechatAuthorizationPhonePO po);

    @Operation(summary = "WEB_发送登录验证码")
    @GetMapping("/web/broker/sendCode")
    ApiResponse<?> sendCode(@RequestParam(name = "phone") String phone);

    @Operation(summary = "WEB_用户登录")
    @PostMapping("/web/broker/login")
    ApiResponse<LoginInfoDTO> login(@RequestBody @Valid LoginInfoPO po);

    @Operation(summary = "WEB_用户退出登录")
    @PostMapping("/web/broker/logout")
    ApiResponse<?> logout(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken);

    @Operation(summary = "WEB_查询用户信息")
    @GetMapping("/web/broker/info")
    ApiResponse<BrokerDTO> queryInfo(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken);

    @Operation(summary = "WEB_查询用户信息(无登录态)")
    @GetMapping("/web/broker/queryInfo")
    ApiResponse<BrokerDTO> queryInfo(@RequestParam(value = "userId") Long userId);

    @Operation(summary = "WEB_用户编辑")
    @PostMapping("/web/broker/edit")
    ApiResponse<BrokerDTO> edit(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
                                @RequestBody @Valid EditBrokerPO editBrokerPO);

    @Operation(summary = "WEB_更新用户城市")
    @PostMapping("/web/broker/updateCity")
    ApiResponse<BrokerDTO> updateCity(
            @RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
            @RequestBody UpdateCityPo po);

    @Operation(summary = "WEB_查询banner信息")
    @GetMapping("/web/broker/banners")
    ApiResponse<List<BannerDTO>> banners(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken);

    @Operation(summary = "WEB_查询积分配置信息 传参xgDescribe 获取积分说明 不传 积分配置")
    @GetMapping("/web/broker/pointConfig")
    ApiResponse<List<PointConfigDTO>> pointConfig(@RequestParam(name = "category", required = false) String category);

    @Operation(summary = "WEB_查询积分变更列表")
    @GetMapping("/web/broker/pointDetail")
    ApiResponse<List<PointDetailDTO>> pointDetail(@RequestAttribute(value = LOGIN_INFO_ATT) SaasLoginToken loginToken,
                                                  @RequestParam(name = "page", defaultValue = "1") Integer page,
                                                  @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize);

    @Operation(summary = "WEB_微信关注回调验证")
    @RequestMapping(value = "/web/broker/wechatCallback", method = {RequestMethod.GET, RequestMethod.POST})
    String wechatCallback(@RequestParam(value = "signature", required = false) String signature,
                          @RequestParam(value = "timestamp", required = false) String timestamp,
                          @RequestParam(value = "nonce", required = false) String nonce,
                          @RequestParam(value = "echostr", required = false) String echostr,
                          HttpServletRequest request);

    @Operation(summary = "WEB_校验openId是否存在")
    @GetMapping("/api/broker/checkUser")
    ApiResponse<Boolean> checkUser(@RequestParam(value = "openId") String openId);

    @Operation(summary = "WEB_生成月度报告")
    @GetMapping("/api/broker/createMonthlyMarketAnalysis")
    void createMonthlyMarketAnalysis();

    @Operation(summary = "WEB_flow通知")
    @GetMapping("/api/broker/flowCallBack")
    ApiResponse<?> flowCallBack(@RequestParam(value = "brokerTaskId")Long brokerTaskId,
                                @RequestParam(value = "status") FlowDTO.Status status,
                                @RequestParam(value = "flowContent")String flowContent);

    @Operation(summary = "WEB_生成政策分析报告")
    @GetMapping("/api/broker/createAnalysisOfIndustryPolicies")
    void createAnalysisOfIndustryPolicies(@RequestParam(value = "cityId")String cityId,
                                          @RequestParam(value = "cityName")String cityName,
                                          @RequestParam(value = "content")String content);
}
