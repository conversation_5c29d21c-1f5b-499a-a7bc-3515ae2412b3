package com.dcai.aixg.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Tag(name = "企微接口")
@FeignClient(name = "aixg", path = "/aixg", contextId = "enterpriseWeChat")
public interface EWeChar {
    @Operation(summary = "验证回调地址")
    @GetMapping("/ewechar/callback")
    String verifyCallback(
            @RequestParam("msg_signature") String sign,
            @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce,
            @RequestParam("echostr") String echostr
    );
    
    @Operation(summary = "接收回调通知")
    @PostMapping("/ewechar/callback")
    String receiveCallback(
            @RequestParam("msg_signature") String sign,
            @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce,
            @RequestBody String echostr
    );
}
