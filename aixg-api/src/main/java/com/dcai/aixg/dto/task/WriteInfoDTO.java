package com.dcai.aixg.dto.task;

import java.util.Arrays;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WriteInfoDTO {

    @Schema(name = "serviceTypeList", description = "服务类型")
	private List<String> serviceTypeList;

    @Schema(name = "specialtyList", description = "特色服务或专长")
	private List<String> specialtyList;

//    @Schema(description = "房源推荐软文")
//	private TaskInfoDTO recommend;
//
//    @Schema(description = "购房指南")
//	private TaskInfoDTO guide;
//
//    @Schema(description = "行业热点评论")
//	private TaskInfoDTO industry;
    
    
    public WriteInfoDTO() {
    	this.serviceTypeList = Arrays.asList("免费带看", "价格评估", "产权尽调", "市场解读", "谈判议价", "合同指导", "过户代办", "贷款咨询", "房屋托管", "法律 咨询", "资产配置建议");
    	this.specialtyList = Arrays.asList("买卖专家", "金融服务专家", "租售专家", "豪宅专家", "老洋房专家", "历史建筑", "投资分析专家", "税费优化专家");
//    	this.recommend = new TaskInfoDTO(Arrays.asList("这套慧芝湖花园的三室户正对小区湖景是楼王位置，当初新房单价全小区最贵，现在按均价急售。"), 
//    			"你可以补充输入针对房源的特色描述，越丰富的内容补充，可以使得文章生成结果越有价值。如果没有补充内容，也可以选择跳过。");
//    	this.guide = new TaskInfoDTO(Arrays.asList("这套慧芝湖花园的三室户正对小区湖景是楼王位置，当初新房单价全小区最贵，现在按均价急售。"), 
//    			"你可以输入找房客的目标价格区位、住房人数、特殊偏好、等明确的购房需求，我将基于你录入的房源，为你创作文章推荐。");
//    	this.industry = new TaskInfoDTO(Arrays.asList("市场疯传房产税即将出台 买房的要倒霉，我认为不会那么快也不会一刀切，建议有需求的人还是可以适度买房投资"), 
//    			"你可以输入市场行业热议话题，并给出自己的结论，我将为你创作文章论述。");
    }

}
