package com.dcai.aixg.pro.task;

import org.apache.commons.lang3.StringUtils;

import com.ejuetc.commons.base.exception.BusinessException;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class WriteCreateUserInfoPO {

    @Schema(name = "companyName", description = "公司简称")
	private String companyName;

    @Schema(name = "education", description = "学历")
	private String education;

    @Schema(name = "workingExperience", description = "工作经验")
	private String workingExperience;

    @Schema(name = "serviceType", description = "服务类型(多个用,隔开)")
	private String serviceType;

    @Schema(name = "specialty", description = "特色服务或专长(多个用,隔开)")
	private String specialty;
    
    public void checkParams() {
    	if (StringUtils.isBlank(companyName)) throw new BusinessException("bc.cpm.aixg.1012", "公司简称");
//    	if (StringUtils.isBlank(education)) throw new BusinessException("bc.cpm.aixg.1012", "学历");
    	if (StringUtils.isBlank(workingExperience)) throw new BusinessException("bc.cpm.aixg.1012", "工作经验");
    	if (StringUtils.isBlank(serviceType)) throw new BusinessException("bc.cpm.aixg.1012", "服务类型");
    	if (StringUtils.isBlank(specialty)) throw new BusinessException("bc.cpm.aixg.1012", "特色服务或专长");
    }

}
