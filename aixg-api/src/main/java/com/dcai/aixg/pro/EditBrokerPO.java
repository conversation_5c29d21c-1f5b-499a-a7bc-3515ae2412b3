package com.dcai.aixg.pro;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class EditBrokerPO {

    @Schema(description = "头像")
    private String icon;

    @NotBlank(message = "请输入姓名")
    @Schema(description = "名称")
    private String name;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "简介")
    private String description;

    @Schema(description = "二维码")
    private String qrCode;

    @Schema(description = "商圈id")
    private String townId;

    @Schema(description = "商圈名称")
    private String townName;
}
