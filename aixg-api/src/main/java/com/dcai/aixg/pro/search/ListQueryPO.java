package com.dcai.aixg.pro.search;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import com.dcai.aixg.dto.task.TaskDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ListQueryPO {
	
    @Schema(name = "writeType", description = "类型 11:政策解读专家 12:价值评测专家 13:房产生活专家 14:房源专家 15:月度市场专家 16:行业政策分析 17:价值评测报告")
    private String writeType;
    
    @Schema(name = "subType", description = "风格类型 ORIGIN:专业长文 REDNOTE:小红书 WECHAT:微信朋友圈")
    private TaskDTO.SubType subType;

    //@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(name = "startDate", description = "开始日期")
    private LocalDate startDate;

    //@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(name = "endDate", description = "结束日期")
    private LocalDate endDate;

    private LocalDateTime startDateTime;

    private LocalDateTime endDateTime;
    
    @Schema(name = "status", description = "状态 ING:生成中 DONE:已生成 FAIL:生成失败")
    private TaskDTO.Status status;
	
    @Schema(name = "keyword", description = "搜索关键字")
    private String keyword;

    @Schema(name = "page", description = "当前页")
    private Integer page = 1;

    @Schema(name = "pageSize", description = "页面条数")
    private Integer pageSize = 10;
    
    public void buildParams() {
    	if (startDate != null) startDateTime = LocalDateTime.of(startDate, LocalTime.of(0, 0, 0));
    	if (endDate != null) endDateTime = LocalDateTime.of(endDate, LocalTime.of(23, 59, 59));
    }

}
