package com.dcai.aixg.domain.aiagent.node;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.domain.aiagent.config.Config4Node;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.domain.task.Task;
import com.dcai.aixg.domain.task.TaskRpt;
import com.dcai.aixg.dto.NodeDTO;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@DiscriminatorValue("GENERATE_IMAGE")
@SubtypeCode(parent = Node.class, code = "GENERATE_IMAGE", name = "生成图片")
public class Node4GenerateImage extends Node {

    protected Node4GenerateImage(Config4Node config, JSONObject request, Flow flow) {
        super(config, request, flow);
    }

    @Override
    protected NodeDTO.Status doExec() throws Exception {
        Task task = getBean(TaskRpt.class).findById(getRequest().getLong("taskId")).orElse(null);
        if (task == null) throw new RuntimeException("任务不存在");
        List<String> chapters = getRequest().getJSONArray("chapters").toJavaList(String.class);
        List<String> imageUrls = task.generatorImage(chapters);
        setResponse(JSONObject.toJSONString(imageUrls));
        return NodeDTO.Status.SUCCESS;
    }
}
