package com.dcai.aixg.domain.task;

import static jakarta.persistence.LockModeType.PESSIMISTIC_WRITE;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.dto.task.TaskDTO;
import com.dcai.aixg.pro.search.ListQueryPO;

public interface TaskRpt extends JpaRepositoryImplementation<Task, Long> {

    @Lock(PESSIMISTIC_WRITE)
    Task findAndLockById(Long id);
    
    @Query("from Write where id = ?1")
    Write findByTaskId(Long id);
    
    @Query("from Write where flowId = ?1")
    Write findByFlowId(Long flowId);
    
    //@Lock(PESSIMISTIC_WRITE)
    @Query("from Write where broker = ?1 and subType in ?2 order by createTime desc")
    List<Write> findAllBySubTypes(Broker broker, List<TaskDTO.SubType> subTypes);

    @Query("from Write where articleId = ?1")
    @Lock(PESSIMISTIC_WRITE)
    List<Write> findAndLockByArticleId(String articleId);

    @Query("from Write where writeId = ?1")
    @Lock(PESSIMISTIC_WRITE)
    List<Write> findAndLockByWriteId(String writeId);

    @Query("from Write where cricDataId = ?1")
    List<Write> findByCricDataId(String cricDataId);

    @Query("from Report where reportId = ?1")
    @Lock(PESSIMISTIC_WRITE)
    List<Report> findAndLockByReportId(String reportId);

    @Query("from Task where openId = :openId and status = :status")
    List<Task> findByOpenIdAndStatus(String openId, TaskDTO.Status status);

    @Query("from Write where status = 'ING' and createTime < ?1")
    List<Write> findAll4IngBeforeDateTime(LocalDateTime createTime);

    @Query("from Write where broker = ?1 and status = 'ING' and subType = 'ORIGIN' and flowId is not null")
    List<Write> findAll4Ing(Broker broker);
    
    @Query("""
            from Write w
            where w.broker = ?1
            AND w.flowId is not null
            AND (:#{#po.writeType} IS NULL OR w.writeType = :#{#po.writeType})
            AND (:#{#po.subType} IS NULL OR w.subType = :#{#po.subType})
            AND (:#{#po.status} IS NULL OR w.status = :#{#po.status})
            AND (:#{#po.startDateTime} IS NULL OR w.createTime >= :#{#po.startDateTime})
            AND (:#{#po.endDateTime} IS NULL OR w.createTime <= :#{#po.endDateTime})
            order by w.createTime desc
            """)
    List<Write> findAllWrites(Broker broker, ListQueryPO po);

}
