package com.dcai.aixg.domain.aiagent.flow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.BrokerAPI;
import com.dcai.aixg.api.WriteAPI;
import com.dcai.aixg.domain.aiagent.commons.ProcessBase;
import com.dcai.aixg.domain.aiagent.config.Config;
import com.dcai.aixg.domain.aiagent.config.Config4Node;
import com.dcai.aixg.domain.aiagent.node.Node;
import com.dcai.aixg.domain.aiagent.node.Node4GenerateImage;
import com.dcai.aixg.domain.aiagent.node.Node4LlmGen;
import com.dcai.aixg.domain.aiagent.node.NodeRpt;
import com.dcai.aixg.dto.FlowDTO;
import com.dcai.aixg.dto.NodeDTO;
import com.dcai.aixg.dto.ProcessDTO;
import com.dcai.aixg.pro.LaunchFlowPO;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.commons.base.usertype.MapContentUT;
import com.ejuetc.commons.base.valueobj.MapContent;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.dcai.aixg.dto.FlowDTO.Status.PROCESSING;
import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.ThreadUtils.runInThreadPool;

/**
 * 工作流实例实体
 * <p>
 * 表示一个具体的工作流执行实例，按顺序执行多个Generate任务
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("工作流实例")
@Table(name = "tb_ai_flow")
@Where(clause = "logic_delete = 0")
public class Flow extends ProcessBase<Flow> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) COMMENT '流程ID'")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "root_config_id", columnDefinition = "bigint(20) COMMENT '工作根节点配置ID'", nullable = false)
    private Config4Node rootConfig;

    @Type(JsonUT.class)
    @Column(name = "request", columnDefinition = "text COMMENT '请求内容'", nullable = false)
    private JSONObject request;

    @Enumerated(EnumType.STRING)
    @Comment("流程状态")
    @Column(name = "status", nullable = false)
    private FlowDTO.Status status;

    @OrderColumn(name = "sort", columnDefinition = "int COMMENT '步骤顺序'")
    @OneToMany(mappedBy = "flow", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Node> nodes = new ArrayList<>();

    @Comment("当前处理器类型")
    @Enumerated(EnumType.STRING)
    @Column(name = "current_process_type")
    private ProcessDTO.Type currentProcessType;

    @Column(name = "current_process_id", columnDefinition = "bigint(20) COMMENT '当前处理器ID'")
    private Long currentProcessId;

    @Comment("通知状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "src_type")
    private FlowDTO.SrcType srcType;

    @Column(name = "src_id", columnDefinition = "bigint(20) COMMENT '发起流程来源ID'")
    private Long srcId;

    @Comment("通知状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "notify_status")
    private ResponseStatus notifyStatus;

    @Column(name = "notify_message", columnDefinition = "text COMMENT '通知消息'")
    private String notifyMessage;

    @Type(MapContentUT.class)
    @Column(name = "properties", columnDefinition = "varchar(511) COMMENT '流程属性配置'")
    private MapContent properties = new MapContent();

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "current_config_id", columnDefinition = "bigint(20) COMMENT '当前配置ID'")
    private Config currentConfig;

    @Transient
    private ProcessBase<?> currentProcess;

    /**
     * 基于PO构造函数
     */
    public Flow(Config4Node rootConfig, LaunchFlowPO po) {
        this.srcType = po.getSrcType();
        this.srcId = po.getSrcId();
        this.rootConfig = rootConfig;
        this.properties = rootConfig.getProperties();
        this.request = new JSONObject(po.getRequest());
        this.status = FlowDTO.Status.WAIT;
    }

    public ProcessBase<?> createNextProcess() {
        if (isCompleted()) return null;

        //因异步截断的流程,当前节点若执行未成功,则直接返回用于终止流程
        if (getCurrentProcess() != null && !getCurrentProcess().isSuccess()) {
            makeCompleted(getCurrentProcess());
            return getCurrentProcess();
        }

        if (currentConfig == null) {
            timerStart();
            status = PROCESSING;
            currentConfig = rootConfig;
        } else {
            currentConfig = currentConfig.nextNode(this);
        }
        if (currentConfig != null) {
            ProcessBase<?> newProcess = currentConfig.newProcess(this);
            newProcess.save();
            setCurrentProcess(newProcess);
            return newProcess;
        } else {
            makeCompleted(getCurrentProcess());
            return null;
        }
    }

    private void setCurrentProcess(ProcessBase<?> processBase) {
        currentProcessType = processBase.getProcessType();
        currentProcessId = processBase.getId();
        currentProcess = processBase;
        if (processBase instanceof Node node)
            nodes.add(node);
    }

    private ProcessBase<?> getCurrentProcess() {
        if (currentProcess == null && currentProcessType != null && currentProcessId != null)
            currentProcess = switch (currentProcessType) {
                case FLOW -> getBean(FlowRpt.class).getReferenceById(currentProcessId);
                case GENERATE -> getBean(NodeRpt.class).getReferenceById(currentProcessId);
                default -> throw new CodingException("意料之外的处理器类型[%s]", currentProcessType);
            };
        return currentProcess;
    }

    public Node getCurrentNode() {
        if (CollectionUtils.isEmpty(nodes)) {
            return null;
        }
        return nodes.get(nodes.size() - 1);
    }

    public void makeCompleted(ProcessBase<?> process) {
        if (!process.equals(currentProcessType, currentProcessId)) throw new IllegalArgumentException("最后一个生成任务不匹配");
        if (isCompleted()) return;
        timerStop();
        status = process.isSuccess() ? FlowDTO.Status.SUCCESS : FlowDTO.Status.FAILED;
        setErrorMessage(process.getErrorMessage());
        setResponse(process.getResponse());
    }

    public boolean isCompleted() {
        return status == FlowDTO.Status.SUCCESS || status == FlowDTO.Status.FAILED;
    }

    @Override
    public ProcessDTO.Type getProcessType() {
        return ProcessDTO.Type.FLOW;
    }

    public boolean isSuccess() {
        return status == FlowDTO.Status.SUCCESS;
    }

    public boolean isFailed() {
        return status == FlowDTO.Status.FAILED;
    }

    public String getPrevGenerateResponse(Node node) {
        int currentIndex = nodes.indexOf(node);
        if (currentIndex <= 0) {
            return null; // 第一个Generate或者找不到当前Generate
        }
        Node prevNode = nodes.get(currentIndex - 1);
        return prevNode != null ? prevNode.getResponse() : null;
    }

    public void notifySource() {
        String notifyResponse = getNotifyResponse();
        ApiResponse<?> notifyResp = runInThreadPool(() -> {
            try {
                return switch (srcType) {
                    case TASK -> getBean(WriteAPI.class).notifyWriteResult(srcId, status, notifyResponse);
                    case TWEET -> getBean(BrokerAPI.class).flowCallBack(srcId, status, notifyResponse);
                    case SUBFLOW -> {
                        getBean(FlowService.class).execFlow(srcId);
                        yield succ();
                    }
                };
            } catch (Exception e) {
                return apiResponse(e);
            }
        });

        notifyStatus = notifyResp.getStatus();
        notifyMessage = notifyResp.getMessage();
    }

    private String getNotifyResponse() {
        String notifyResponse = getResponse();
        if (nodes.stream().anyMatch(t -> t.getType() == NodeDTO.Type.GENERATE_IMAGE)) {
            String content = "";
            List<String> chapterImages = new ArrayList<>();
            String title = "";
            for (Node node : nodes) {
                if (node instanceof Node4LlmGen) {
                    content = node.getResponse();
                    title = content.contains("【正文】") ? content.split("【正文】")[0].split("【标题】")[1] : content.split("【朋友圈文案】")[0].split("【标题】")[1];
                }
                if (node instanceof Node4GenerateImage) {
                    chapterImages = JSONArray.parseArray(node.getResponse()).toJavaList(String.class);
                }
            }
            notifyResponse = JSONObject.toJSONString(Map.of("title", title, "content", content, "chapterImages", chapterImages));
        }
        return notifyResponse;
    }

    public boolean isWait() {
        return status == FlowDTO.Status.WAIT;
    }

    public Flow getParentFlow() {
        if (!hasParentFlow())
            throw new CodingException("流程[%s]的来源类型为[%s],没有父流程", getId(), getSrcType());
        return getBean(FlowRpt.class).getReferenceById(srcId);
    }

    public boolean hasParentFlow() {
        return srcType == FlowDTO.SrcType.SUBFLOW;
    }

    public Flow getOriginationFlowId() {
        if (srcType != FlowDTO.SrcType.SUBFLOW) {
            return this;
        }
        return getBean(FlowRpt.class).getReferenceById(srcId).getOriginationFlowId();
    }

    public String getGenerateResponse(String generateConfigCode) {
        return nodes.stream()
                .filter(g -> g.isConfigCode(generateConfigCode))
                .map(Node::getResponse)
                .findFirst().orElseThrow(() -> new CodingException("未找到生成器:%s", generateConfigCode));
    }
}
