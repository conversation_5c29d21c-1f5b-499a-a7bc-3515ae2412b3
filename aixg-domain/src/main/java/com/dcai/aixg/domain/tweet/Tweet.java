package com.dcai.aixg.domain.tweet;


import com.dcai.aixg.api.WriteAPI;
import com.dcai.aixg.domain.aiagent.flow.FlowService;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.domain.broker.BrokerRpt;
import com.dcai.aixg.dto.FlowDTO;
import com.dcai.aixg.dto.TweetDTO;
import com.dcai.aixg.pro.LaunchFlowPO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.utils.StringUtils;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.dcai.aixg.dto.FlowDTO.SrcType.TWEET;
import static com.dcai.aixg.pro.LaunchFlowPO.RunMode.SYNC;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

/**
 * 任务
 */
@Entity
@Table(name = "tb_tweet")
@Comment("推文任务")
@Where(clause = "logic_delete = 0")
@NoArgsConstructor
@Getter
@Slf4j
@Accessors(chain = true)
public class Tweet extends BaseEntity<Tweet> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) COMMENT 'ID'")
    private Long id;

    @Column(name = "year", columnDefinition = "varchar(20) COMMENT '年'")
    private String year;

    @Column(name = "month", columnDefinition = "varchar(20) COMMENT '月'")
    private String month;

    @Column(name = "city_id", columnDefinition = "varchar(20) COMMENT '城市id'")
    private String cityId;

    @Column(name = "city_name", columnDefinition = "varchar(50) COMMENT '城市名称'")
    private String cityName;

    @Column(name = "town_id", columnDefinition = "varchar(50) COMMENT '商圈id'")
    private String townId;

    @Column(name = "town_name", columnDefinition = "varchar(50) COMMENT '商圈名称'")
    private String townName;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, updatable = false)
    private TweetDTO.Type type;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TweetDTO.Status status = TweetDTO.Status.WAIT;

    @Column(name = "msg", columnDefinition = "varchar(255) COMMENT '任务信息'")
    private String msg;

    @Column(name = "flow_id", columnDefinition = "bigint(20) COMMENT '流程id'")
    private Long flowId;

    @Column(name = "flow_content", columnDefinition = "longtext COMMENT '报告内容'")
    private String flowContent;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "broker_id", columnDefinition = "bigint(20) COMMENT '经纪人ID'")
    protected Broker broker;

    public Tweet(TweetDTO.Type type, Broker broker, String cityId, String cityName, String townId, String townName, String year, String month, String msg) {
        this.type = type;
        this.cityId = cityId;
        this.cityName = cityName;
        this.townId = townId;
        this.townName = townName;
        this.year = year;
        this.month = month;
        this.msg = msg;
        this.broker = broker;
    }

    public Tweet(TweetDTO.Type type, Broker broker, String cityId, String cityName, String msg) {
        this.type = type;
        this.cityId = cityId;
        this.cityName = cityName;
        this.msg = msg;
        this.broker = broker;
    }

    public void createFlow() {
        ApiResponse<FlowDTO> response = getBean(FlowService.class).launch(new LaunchFlowPO().setSrcType(TWEET)
                .setSrcId(getId())
                .setRequest(type == TweetDTO.Type.MONTHLY_REPORT ? Map.of("writeType", 15, "cityName", cityName, "areaName", townName, "userId", broker.getKerId()) : Map.of("writeType", 16, "cityName", cityName, "policy", msg, "userId", broker.getKerId()))
                .setConfigCode(type.name())
                .setRunMode(SYNC)
        );
        this.status = response.isSucc() && response.getData().getStatus() != FlowDTO.Status.FAILED ? TweetDTO.Status.ING : TweetDTO.Status.FAIL;
        this.flowId = response.getData().getId();
        getBean(TweetRpt.class).save(this);
    }

    public void flowComplete(FlowDTO.Status flowStatus, String flowContent) {
        this.status = flowStatus == FlowDTO.Status.SUCCESS ? TweetDTO.Status.DONE : TweetDTO.Status.FAIL;
        this.flowContent = flowContent;
        getBean(TweetRpt.class).save(this);
        if (this.status == TweetDTO.Status.DONE) {
            List<Long> brokerIds = new ArrayList<>();
            BrokerRpt brokerRpt = getBean(BrokerRpt.class);
            if (type == TweetDTO.Type.MONTHLY_REPORT) {
                brokerIds = brokerRpt.findIdsByTownId(townId);
            } else if (type == TweetDTO.Type.INDUSTRY_ANALYST) {
                brokerIds = StringUtils.notBlank(cityId) ? brokerRpt.findIdsByCityId(cityId) : brokerRpt.findIds();
            }
            brokerIds.forEach(t -> getBean(WriteAPI.class).createWrite4BrokerByFlowId(t, flowId, type));
        }
    }
}
