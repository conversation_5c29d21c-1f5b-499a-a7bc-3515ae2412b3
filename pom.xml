<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ejuetc.commons</groupId>
        <artifactId>commons</artifactId>
        <version>0.7.1</version>
    </parent>

    <groupId>com.dcai.aixg</groupId>
    <artifactId>aixg</artifactId>
    <packaging>pom</packaging>
    <version>0.1.8</version>
    <modules>
        <module>aixg-api</module>
        <module>aixg-application</module>
        <module>aixg-domain</module>
        <module>aixg-integration</module>
    </modules>

    <properties>
        <commons.version>0.7.6</commons.version>
        <rocketmq.version>2.2.0</rocketmq.version>
        <gateway.version>0.0.4</gateway.version>
        <saasapi.version>0.0.5</saasapi.version>
        <message.version>0.1.0</message.version>
        <consumer.version>0.2.2</consumer.version>
        <spring-ai.version>1.0.0</spring-ai.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ejuetc.saasapi</groupId>
                <version>${saasapi.version}</version>
                <artifactId>saasapi-api</artifactId>
            </dependency>
            <dependency>
                <groupId>com.ejuetc.consumer</groupId>
                <artifactId>consumer-api</artifactId>
                <version>${consumer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcai.message</groupId>
                <artifactId>message-api</artifactId>
                <version>${message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dcai.gateway</groupId>
                <artifactId>gateway-sdk</artifactId>
                <version>${gateway.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ejuetc.commons</groupId>
                <artifactId>commons-base</artifactId>
                <version>${commons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ejuetc.commons</groupId>
                <artifactId>commons-querydomain</artifactId>
                <version>${commons.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>