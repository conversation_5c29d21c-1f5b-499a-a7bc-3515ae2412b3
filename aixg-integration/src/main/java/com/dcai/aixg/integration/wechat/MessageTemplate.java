package com.dcai.aixg.integration.wechat;

import com.ejuetc.commons.base.entity.TitleEnum;
import lombok.Getter;

@Getter
public enum MessageTemplate implements TitleEnum {
    ARTICLE("文章", "diaSC2exTVPLBEygxWjbqxZiaTgMx5qaVWTDyp6s6o4", "pages/articles/detail?type=new&id=", "wxed8e35c2a3494df6"),
    REPORT("报告", "diaSC2exTVPLBEygxWjbqxZiaTgMx5qaVWTDyp6s6o4", "pages/reports/detail?type=new&id=", "wxed8e35c2a3494df6"),
    SHARE("分享", "diaSC2exTVPLBEygxWjbqxZiaTgMx5qaVWTDyp6s6o4", "pages/articles/template?id=", "wxed8e35c2a3494df6");

    private final String title;
    private final String templateId;
    private final String pagePath;
    private final String appId;

    MessageTemplate(String title, String templateId, String pagePath, String appId) {
        this.title = title;
        this.templateId = templateId;
        this.pagePath = pagePath;
        this.appId = appId;
    }

    @Override
    public String toString() {
        return name() + "-" + title;
    }
}
