package com.dcai.aixg.integration.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.dto.task.TaskDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Component
public class WechatService {
    private final WechatConfig wechatConfig;
    private final RedisTemplate<String, String> redisTemplate;
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    public String getAccessToken() {
        String key = "aixg:wechat:accessToken";
        String token = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        String accessTokenUrl = wechatConfig.getAccessTokenUrl();
        String resp = restTemplate.getForObject(accessTokenUrl, String.class);
        log.info("wechat resp get access token, resp={}", resp);
        WechatAccessTokenRO wechatAccessTokenRO = fromJson(resp, WechatAccessTokenRO.class);
        if (wechatAccessTokenRO == null || !wechatAccessTokenRO.isSuccess()) {
            return null;
        }
        token = wechatAccessTokenRO.getAccessToken();
        redisTemplate.opsForValue().set(key, token, Duration.ofSeconds(wechatAccessTokenRO.getExpiresIn()));
        return token;
    }

    public String getQrCodeTicket(Long taskId) {
        String accessToken = getAccessToken();
        if (StringUtils.isBlank(accessToken)) {
            return null;
        }
        String accessTokenUrl = wechatConfig.getQrCodeTicket(accessToken);
        log.info("wechat req get qrCode ticket, taskId = {}, url={}", taskId, accessTokenUrl);
//        {"expire_seconds": 604800, "action_name": "QR_SCENE", "action_info": {"scene": {"scene_id": 123}}}
        Map<String, Object> body = new HashMap<>();
        body.put("expire_seconds", 3600*5);
        body.put("action_name", "QR_SCENE");
        Map<String, Object> action_info = new HashMap<>();
        action_info.put("scene", Map.of("scene_id", taskId));
        body.put("action_info", action_info);
        String resp = restTemplate.postForObject(accessTokenUrl, JSON.toJSONString(body), String.class);
        log.info("wechat resp get qrCode ticket, taskId={}, resp={}", taskId, resp);
        WechatQrCodeTicketRO ro = fromJson(resp, WechatQrCodeTicketRO.class);
        if (ro == null) {
            return null;
        }
        try {
            return wechatConfig.getQrCodeUrl(URLEncoder.encode(ro.getTicket(), "UTF-8"));
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public Boolean sendMessage(Long messageId, String title, LocalDateTime time, String openId, MessageTemplate messageTemplate, Long taskId, TaskDTO.SubType subType) {
        String accessToken = getAccessToken();
        if (StringUtils.isBlank(accessToken)) {
            return false;
        }
        String accessTokenUrl = wechatConfig.getMessageUrl(accessToken);

        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=UTF-8");

        log.info("wechat req send message, openId = {}, url={}", openId, accessTokenUrl);
        Map<String, Object> param = new HashMap<>();
        param.put("touser", openId);
        param.put("template_id", messageTemplate.getTemplateId());
        String pagepath = messageTemplate.getPagePath() + taskId;
        if (subType != TaskDTO.SubType.ORIGIN) {
            pagepath = pagepath + "&type=" + subType.name().toLowerCase();
        }
        param.put("miniprogram", Map.of("appid", messageTemplate.getAppId(), "pagepath", pagepath));
        param.put("client_msg_id", messageId);
        String thing25Str = title.length() > 20 ? title.substring(0, 17) + "..." : title;
        thing25Str = new String(thing25Str.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
        Map<String, String> keyword1 = Map.of("value", thing25Str);
        Map<String, String> keyword2 = Map.of("value", time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        param.put("data", Map.of("thing25", keyword1, "time16", keyword2));
        log.info("wechat sendMessage url = {}, req = {}", accessTokenUrl, JSON.toJSONString(param));
        // 创建请求体HttpEntity
        HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(param), headers);
        // 发送请求
        ResponseEntity<String> resp = restTemplate.exchange(accessTokenUrl, HttpMethod.POST, entity, String.class);
        JSONObject jsonObject = JSONObject.parseObject(resp.getBody());
        log.info("wechat sendMessage url = {}, req = {}, resp = {}, jsonObject = {}", accessTokenUrl, JSON.toJSONString(param), resp, jsonObject);
        return jsonObject.containsKey("errcode") && jsonObject.getString("errcode").equals("0");
    }

    public void sendMessageToWeChat(String openId, String message) {
        // 获取 access_token（确保 token 是有效的）
        String accessToken = getAccessToken();
        // 请求的 URL
        String url = wechatConfig.getSendMessageUrl(accessToken);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=UTF-8");

        // 构造消息内容
        Map<String, Object> messageContent = new HashMap<>();
        messageContent.put("touser", openId);
        messageContent.put("msgtype", "text");

        Map<String, String> text = new HashMap<>();
        text.put("content", message);
        messageContent.put("text", text);

        // 创建请求体HttpEntity
        HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(messageContent), headers);
        // 发送请求
        log.info("wechat sendMessageToWeChat url = {}, req = {}", url, JSON.toJSONString(messageContent));
        ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        JSONObject jsonObject = JSONObject.parseObject(resp.getBody());
        log.info("wechat sendMessageToWeChat url = {}, req = {}, resp = {}, jsonObject = {}", url, JSON.toJSONString(messageContent), resp, jsonObject);
    }


    @SneakyThrows
    private <T> T fromJson(String json, Class<T> clazz) {
        return objectMapper.readValue(json, clazz);
    }
}
